{"description": "项目配置文件，详见文档：https://developers.weixin.qq.com/miniprogram/dev/devtools/projectconfig.html", "packOptions": {"ignore": [], "include": []}, "setting": {"urlCheck": true, "es6": true, "enhance": true, "postcss": true, "preloadBackgroundData": false, "minified": true, "newFeature": false, "coverView": true, "nodeModules": true, "autoAudits": false, "showShadowRootInWxmlPanel": true, "scopeDataCheck": false, "uglifyFileName": true, "checkInvalidKey": true, "checkSiteMap": true, "uploadWithSourceMap": true, "compileHotReLoad": false, "lazyloadPlaceholderEnable": false, "useMultiFrameRuntime": true, "useApiHook": true, "useApiHostProcess": true, "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "enableEngineNative": false, "useIsolateContext": true, "userConfirmedBundleSwitch": false, "packNpmManually": false, "packNpmRelationList": [], "minifyWXSS": true, "disableUseStrict": false, "showES6CompileOption": false, "useCompilerPlugins": false, "minifyWXML": true, "ignoreUploadUnusedFiles": true, "useStaticServer": true}, "compileType": "miniprogram", "libVersion": "2.15.0", "appid": "wxfb9a1451a1682ca4", "projectname": "energy-web-wxapp-flow-view", "condition": {"plugin": {"list": []}, "game": {"list": []}, "gamePlugin": {"list": []}, "miniprogram": {"list": [{"name": "Main", "pathName": "pages/station/station", "query": "", "scene": 1007}, {"name": "<PERSON><PERSON>", "pathName": "pages/authorization/authorization", "query": "", "scene": null}, {"name": "Checkstand", "pathName": "pages/station/checkstand/checkstand", "query": "address=vsdfsdfsf&name=正佳广场油站第九九站&memberPrice=5&otherPirce=6.99&discount=1.99&distance=3201.3110407468907&mchNo=100000000000061&longitude=113.334426&latitude=23.138319&", "scene": null}, {"name": "Order", "pathName": "pages/order/order", "query": "", "scene": null}, {"name": "OrderDetails", "pathName": "pages/order/details/orderDetails", "query": "orderNo=W_W400121022500007859", "scene": null}, {"name": "<PERSON><PERSON>", "pathName": "pages/reward/reward", "query": "", "scene": null}, {"name": "My Reward", "pathName": "pages/reward/myReward/myReward", "query": "", "scene": null}, {"name": "Activity Rules", "pathName": "pages/reward/activityRules/index", "query": "", "scene": null}, {"name": "Account Record", "pathName": "pages/reward/myReward/accountRecord/index", "query": "", "scene": null}, {"name": "From Wechat Share", "pathName": "pages/station/station", "query": "inviterId=xxxxxx99", "scene": 1007}, {"name": "Order Details From Checkstank", "pathName": "pages/order/details/orderDetails", "query": "orderNo=W_W400121030200007932", "scene": null}, {"name": "Share Image", "pathName": "pages/reward/shareImage/index", "query": "orderNo=W_W400121030200007932", "scene": null}, {"name": "From Scan QRcode", "pathName": "pages/station/station", "query": "teamleaderId=*********", "scene": 1012}]}}, "editorSetting": {"tabIndent": "insertSpaces", "tabSize": 2}}