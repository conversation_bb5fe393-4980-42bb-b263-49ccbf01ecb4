# 关于polyfill目录

用于抹平各平台差异化，使小程序转换uniapp项目后，能尽可能的少报错，尽可能的先运行起来。

## 文件结构

### base64Binary.js

用于 base64ToArrayBuffer, arrayBufferToBase64 两个函数的polyfill，因为这两函数仅app与微信小程序支持，特意制作此polyfill。

主要用于polyfill.js文件。

### mixins.js

有两个用途：
一是在使用富文本时，可以将后台传入的富文本字符串里面的转义符转换为普通字符，与mp-html插件配合使用。
二是this.setData()函数的polyfill，使转换后的uniapp项目，可以直接使用setData函数。

### polyfill.js

此文件，对大量api进行判断，如果在当前平台，不支持此函数，将会创建一个空函数，并输出一条提示，提示开发者，这个api需针对性的进行兼容处理。

如果不处理的话，会直接进行报错，并影响流程的运行，对转换者的心理有一定的影响。

因此制作此polyfill，让项目能先运行起来~


## 注意

如果觉得这些文件不需要想删除它，请一定要先阅读关于每个文件的说明，明白它的作用，再进行删除，以免项目运行报错，感谢合作~

如有不明白的地方，请联系作者(<EMAIL>)或qq群(780359397、361784059、603659851)进行交流~

zhangdaren 2021-07-21

