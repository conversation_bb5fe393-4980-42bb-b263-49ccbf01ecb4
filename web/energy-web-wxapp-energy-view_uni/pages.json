{
	"easycom": {
		"^u-(.*)": "uview-ui/components/u-$1/u-$1.vue"
	},
	"pages": [
		{
			"path": "pages/checkstand/checkstand",
			"style": {}
		},
		{
			"path": "pages/completion/completion",
			"style": {
				"navigationBarTitleText": "支付成功"
			}
		}
	],
	"subPackages": [{
		"root": "packageM", // 分包的根目录
		// 该分包下的所有页面
		"pages": [
			{
				"path": "member/member",
				"style": {
					"navigationBarTitleText": "会员中心",
					"navigationBarBackgroundColor": "#EACA8E"
				}
			},
			{
				"path": "member/integral/integral",
				"style": {
					"navigationBarTitleText": "积分明细"
				}
			},
			{
				"path": "member/oilCard/oilCard",
				"style": {
					"navigationBarTitleText": "电子加油卡",
					"navigationBarBackgroundColor": "#EACA8E"
				}
			},
			{
				"path": "member/oilCard/recharge/recharge",
				"style": {}
			},
			{
				"path": "member/oilCard/rechargeResult/rechargeResult",
				"style": {
					"navigationBarTitleText": "充值结果"
				}
			},
			{
				"path": "member/oilCard/transactionRecord/transactionRecord",
				"style": {
					"navigationBarTitleText": "交易记录",
					"enablePullDownRefresh": true
				}
			},
			{
				"path": "member/oilCard/transactionDetail/transactionDetail",
				"style": {
					"navigationBarTitleText": "交易详情"
				}
			},
			{
				"path": "member/oilCard/transactionResult/transactionResult",
				"style": {
					"navigationBarTitleText": "消费成功"
				}
			},
			{
				"path": "member/refuelingRecord/refuelingRecord",
				"style": {
					"navigationBarTitleText": "加油记录",
					"enablePullDownRefresh": true
				}
			},
			{
				"path": "member/refuelingRecord/refuelingDetail/refuelingDetail",
				"style": {
					"navigationBarTitleText": "订单详情"
				}
			},
			{
				"path": "member/coupon/coupon",
				"style": {
					"navigationBarTitleText": "优惠券"
				}
			},
			{
				"path": "member/coupon/expired/expired",
				"style": {
					"navigationBarTitleText": "失效优惠券"
				}
			}
		]
	}],
	"preloadRule": { // 分包预下载规则配置
		"pages/checkstand/checkstand": { // 触发分包预下载的页面路径
			// network 表示在指定的网络模式下进行预下载
			// 可选值为：all（不限网络） 和 wifi（仅 wifi 模式下进行预下载）
			// 默认值为：wifi
			"network": "all",
			// packages 表示进入页面后预下载哪些分包
			// 可以通过 root 或 name 指定预下载哪些分包
			// 如果是 __APP__ 表示下载所有包
			"packages": ["packageM"]
		}
	},
	"sitemapLocation": "sitemap.json",
	"globalStyle": {
		"backgroundTextStyle": "light",
		"navigationBarBackgroundColor": "#fff",
		"navigationBarTextStyle": "black"
	}
}
