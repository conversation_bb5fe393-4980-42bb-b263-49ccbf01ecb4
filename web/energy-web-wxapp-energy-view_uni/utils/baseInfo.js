import { qryBaseInfo, qryFlowOilCardAccount } from '../api/0-common';

// 查询收款码信息&会员信息
export const qryInfo = (global) => {
	return new Promise((resolve, reject) => {
		uni.getStorage({
			key: 'baseInfo',
			complete: (res)=> {
				let reqParams = {
					pcn: global.pcn,
					openId: '',
					type: 0
				};
				
				if(res.errMsg === 'getStorage:ok' || res.success) {
					const { openId, provider } = res.data;
					reqParams.openId = openId;
					reqParams.type = provider.includes('weixin') ? 1 : 2;
					
					global.provider = provider;
				}
				
				qryBaseInfo(reqParams).then(res => {
					if (res.code === 200) {
						return res.data;
					} else {
						uni.showToast({
							title: res.msg,
							icon: 'error',
							duration: 2000
						});
						reject(res.msg);
					}
				}).then(data => {
					console.log(data, 'qryInfo');
				
					const { openFLowOilCardSave } = data.paymentCodeInfoDTO;
					const memberId = data.memberId;
					
					// 重新设置全局变量
					global.userInfo.paymentCodeInfoDTO = data.paymentCodeInfoDTO;
					global.userInfo.memberId = memberId;
					
					if(memberId && openFLowOilCardSave) {
						qryFlowOilCardAccount({ memberId }).then(res=> {
							if (res.code === 200) {
								if (res.data) {
									global.userInfo.oilCardAccount = res.data;
								}
							}
						})
					}
					
					resolve(data);
				});
			}
		})
	})
}