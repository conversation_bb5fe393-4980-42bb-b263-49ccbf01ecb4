import { wxAuth } from '../api/1-checkstand';

export const login = () => {
	const app = getApp();
	const pcn = app.globalData.pcn;
	
    return new Promise((resolve, reject) => {
		// 判断是微信还是支付宝
		uni.getProvider({
			service: 'oauth',
			success: function (res) {
				const provider = res.provider[0];
				if(res.provider.includes('alipay') || res.provider.includes('weixin')) {
					uni.login({
						provider: provider,
					    success: (loginRes) => {
					        if (loginRes.code) {
					            wxAuth({
					                code: loginRes.code,
					                pcn: pcn,
									type: res.provider.includes('weixin') ? 1 : 2
					            }).then((res) => {
					                if (res.code === 200) {
					                    return res.data;
					                } else {
					                    uni.showToast({
					                        title: res.msg,
					                        icon: 'loading',
					                        duration: 2000
					                    });
					                }
					            }).then(({ openId, unionId, token }) => {
									// storage中的信息是针对小程序的，不是某个商户的
									const baseInfo = {
										token,
										provider,
										openId,
										unionId
									}
									
									uni.setStorage({
										key: 'baseInfo',
										data: baseInfo
									})
									
									app.globalData.provider = provider;
									
					                resolve({ provider, openId, token });
					            });
					        } else {
					            uni.showToast({
					                title: '授权失败',
					                icon: 'error',
					                duration: 2000
					            });
					            console.error('授权失败' + wxRes.errMsg);
					            reject('fail');
					        }
					    },
					    fail: (err) => {
					        console.log(err);
					        reject('fail');
					    }
					});
				}
			}
		});
    });
};
