import { commonPay } from '../api/0-common';

const app = getApp();
// 加油支付url
const payUrl = '/miniApi/paymentCode/pay/reqPay';
// 油卡充值url
const rechargeUrl = '/miniApi/paymentCode/oilCard/recharge';
// 有效时间
const activedTime = 600000;

// 请求状态(该状态只适用于前端的支付逻辑判断，与后端无关) 100：成功 101：取消支付 102：支付失败
export const payStatus = {
	success: 100,
	cancel: 101,
	fail: 102,
}

// 下单类型
export const payType = {
	miniPay: 0,
	oilPay: 1
}

// 统一下单接口 
export const commontPayment = function (params) {
	const currentTime = Date.now();
	// <=600000则表示在10分钟内执行了支付操作
	const actived = (currentTime-app.globalData.enterTime) <= activedTime;
	const url = params.type === payType.miniPay ? payUrl : rechargeUrl;
	return new Promise((resolve, reject) => {
		if(actived) {
			commonPay(url, params).then(res => {
			    if (res.code === 200) {
			        return res.data;
			    } else {
			        uni.showModal({
			            title: '支付失败提示',
			            content: res.msg,
			            showCancel: false,
			
			            success(res) {
			                if (res.confirm) {
			                    console.log('用户点击确定');
			                } else if (res.cancel) {
			                    console.log('用户点击取消');
			                }
			            }
			        });
			        
					reject(null);
			    }
			}).then(data => {
			    if (data.paymentStatus === 102 && data.channelResult) {
			        const channelResultStr = data.channelResult;
			        const channelResult = JSON.parse(channelResultStr);
			
			        uni.requestPayment({
			            // #ifdef MP-WEIXIN
			            provider: 'wxpay',
			            timeStamp: channelResult.timeStamp,
			            nonceStr: channelResult.nonceStr,
			            package: channelResult.package,
			            paySign: channelResult.paySign,
			            signType: channelResult.signType,
			            // #endif
			            // #ifdef MP-ALIPAY
			            provider: 'alipay',
			            orderInfo: channelResultStr,
			            // #endif
			            success: res => {
							const result = {
								code: payStatus.success,
								msg: res.errMsg,
								data: data
							}
			                
							// #ifdef MP-ALIPAY
							result.resultCode = res.resultCode;
							// #endif
							
							resolve(result);
			            },
			            fail: res => {
							const result = {
								code: payStatus.cancel,
								msg: res.errMsg,
								data
							}
							reject(result);
			            }
			        });
			    } else {
					const result = {
						code: payStatus.fail,
						msg: '',
						data: data
					}
					reject(result);
			    }
			});
		} else {
			app.globalData.showError('收款码已失效，请重新扫码进入！');
			
			// 清空数据
			app.globalData.userInfo.memberId = '';
			app.globalData.userInfo.paymentCodeInfoDTO = null;
			app.globalData.userInfo.oilCardAccount = null;
			
			reject(null);
		}
	})
}

