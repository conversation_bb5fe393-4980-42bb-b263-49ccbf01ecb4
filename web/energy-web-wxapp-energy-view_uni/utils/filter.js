// filter.wxs
const filters = {
	timeFt: function(time) {
		return time.toString().split('.')[0].replace('T', ' ');
	},
	moneyFt: function(money) {
		return Number(money).toFixed(2);
	},
	orderStatusFt: function(state) {
		const stateEnum = {
			'100': '交易成功',
			'102': '订单未支付',
			'104': '交易失败',
		}
		return state ? stateEnum[(state).toString()] : '';
	},
	lightWeightPointsFt: function(bizType) {
		const lightWeightPointsEnum = {
			'1': '加油积分领取',
			'2': '兑换加油优惠',
			'3': '调账',
			'4': '扣减积分-重新发放',
			'5': '赠送积分-回退',
		};
		return lightWeightPointsEnum[bizType.toString()];
	},
	lightWeightPriorityUseStatusFt: function(status) {
		const lightWeightPointsEnum = {
			'100': '可使用',
			'101': '已使用',
			'102': '已失效',
			'103': '已撤销',
		};
		return lightWeightPointsEnum[status.toString()];
	}
}

export default filters;
