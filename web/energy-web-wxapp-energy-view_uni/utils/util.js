const formatTime = (date) => {
    const year = date.getFullYear();
    const month = date.getMonth() + 1;
    const day = date.getDate();
    const hour = date.getHours();
    const minute = date.getMinutes();
    const second = date.getSeconds();
    return `${[year, month, day].map(formatNumber).join('/')} ${[hour, minute, second].map(formatNumber).join(':')}`;
};

const formatNumber = (n) => {
    n = n.toString();
    return n[1] ? n : `0${n}`;
};

const formatOil = (oilTypeDesc) => {
	const oilTypeEnum = {
	  '0#': '2',
	  '90#': '1',
	  '92#': '1',
	  '95#': '1',
	  '98#': '1',
	  '101#': '1'
	}
	return oilTypeDesc ? oilTypeEnum[oilTypeDesc] : '';
}

module.exports = {
    formatTime,
	formatOil,
};
