<template>
	<view class="completion-container">
		<view class="flex-column flex-center">
			<view class="completion-header flex-center">
				<u-icon size="180" imgMode="widthFix" name="/static/image/public/success.png"></u-icon>
				<view class="completion-header-item flex-column">
					<text class="success fz-45 bold">您已支付成功！</text>
					<view class="completion-header-item-item fz-30">
						<text class="title">商家：</text>
						<text>{{mchName}}</text>
					</view>
					<view class="completion-header-item-item fz-30">
						<text class="title">金额：</text>
						<text class="color-red bold">￥{{orderAmount}}</text>
					</view>
					<view class="completion-header-item-item fz-30">
						<text class="title">下单时间：</text>
						<text> {{createTime | timeFt}} </text>
					</view>
                    <view class="completion-header-item-item fz-30">
                        <text class="title">订单编号：</text>
                        <text>{{payOrderNo}}</text>
                    </view>
				</view>
			</view>
			<view>
				<u-button type="primary" color="#ff9900" :customStyle="isExisted ? btnBiggerCustomStyle : btnCustomStyle" @click="jumpToOther">{{isExisted ? '查看订单详情' : '返回'}}</u-button>
				<navigator target="miniProgram" open-type='exit'>
					<u-button :customStyle="isExisted ? btnCustomStyle : btnBiggerCustomStyle">关闭</u-button>
				</navigator>
			</view>
		</view>
	</view>
</template>

<script>
let app = getApp();

export default {
	data() {
		return {
			btnCustomStyle: {
				width: '75vw',
				height: '7vh',
				borderRadius: '3.5vh',
				marginTop: '3vh'
			},
			btnBiggerCustomStyle: {
				width: '75vw',
				height: '8vh',
				borderRadius: '4vh',
				marginTop: '3vh'
			},
			mchName: '',
			orderAmount: '',
			payOrderNo: '', // 订单编号
			createTime: '', // 下单时间
			isExisted: false, // 是否会员
		}
	},
	/**
	 * 生命周期函数--监听页面加载
	 */
	onLoad: function(options) {
		const { mchName, orderAmount, payOrderNo, createTime } = options;
		
		this.mchName = mchName;
		this.orderAmount = orderAmount;
		this.payOrderNo = payOrderNo;
		this.createTime = createTime;
		
		this.isExisted = app.globalData.userInfo.memberId ? true : false;
	},
	/**
	 * 生命周期函数--监听页面初次渲染完成
	 */
	onReady: function() {},
	/**
	 * 生命周期函数--监听页面显示
	 */
	onShow: function() {
	},
	/**
	 * 生命周期函数--监听页面隐藏
	 */
	onHide: function() {
	},
	/**
	 * 生命周期函数--监听页面卸载
	 */
	onUnload: function() {},
	/**
	 * 页面相关事件处理函数--监听用户下拉动作
	 */
	onPullDownRefresh: function() {},
	/**
	 * 页面上拉触底事件的处理函数
	 */
	onReachBottom: function() {},
	/**
	 * 用户点击右上角分享
	 */
	onShareAppMessage: function() {},
	methods: {
		jumpToOther: function() {
			if(this.isExisted) {
				uni.reLaunch({
					url: '/packageM/member/refuelingRecord/refuelingRecord'
				})
			} else {
				uni.reLaunch({
					url: '/pages/checkstand/checkstand'
				})
			}
		}
	}
}
</script>

<style>
@import './completion.css';
</style>