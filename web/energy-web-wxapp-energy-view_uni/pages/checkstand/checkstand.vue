<template>
	<view>
		<!-- 骨架屏 -->
		<my-skeleton :show="showSkeleton"></my-skeleton>
		<view v-show="!showSkeleton">
			<scroll-view scroll-y="true" :scroll-into-view="toView" style="height: 100vh;">
				<view :class="'keyboard-default ' + (showKeyboard && sysProduct !== 'NON_OIL' ? 'start' : '')">
					<view class="checkstand-header">
						<view class="flex checkstand-header-item">
							<image
								class="checkstand-header-image"
								:src="filePath ? filePath : '/static/image/public/station-default-bg.png'"
								mode="aspectFill"
								:lazy-load="false"
							/>
							<view class="flex-column checkstand-header-info">
								<text class="checkstand-header-title">{{ mchName }}</text>
								<text class="distance-text">{{ address }}</text>
							</view>
							<view class="member flex-column flex-center" v-if="openLightweightMember" @click="jumpToInfo">
								<image class="member-image" src="/static/image/checkstand/member.png" mode="aspectFill" :lazy-load="false" />
								<text>会员中心</text>
							</view>
						</view>
						<view class="flex checkstand-prices-board" v-if="sysProduct !== 'NON_OIL'">
							<view class="current-title flex-column flex-center">
								<text>今日</text>
								<text>油价</text>
							</view>
							<view style="flex-grow: 1;">
								<swiper
									style="height: 100%;"
									:indicator-dots="indicatorDots"
									:autoplay="autoplay"
									:interval="interval"
									:vertical="vertical"
									:circular="circular"
									:duration="duration"
									:current="currIndex"
									@change="changeSwiper"
								>
									<template v-for="(item, index) in oilList">
										<swiper-item :key="index">
											<view class="swiper-item-content flex-center flex-jc-between">
												<view class="swiper-item-content-padding flex-column">
													<text class="swiper-item-title">油品</text>
													<text class="swiper-item-desc">{{ item.oilTypeDesc }}</text>
												</view>
												<view class="flex"><view class="u-border-left" style="height: 4vh;margin: auto;"></view></view>
												<view :class="'swiper-item-content-padding flex-column ' + (canPay ? 'color' : 'color-sp swiper-item-sp')">
													<text class="swiper-item-title">{{ openLightweightMember ? '会员价' : '优惠价' }}</text>
													<text>￥{{ item.memberPrice | moneyFt }}/L</text>
												</view>
												<view class="flex"><view class="u-border-left" style="height: 4vh;margin: auto;"></view></view>
												<view :class="'swiper-item-content-padding flex-column ' + (canPay ? 'color-sp swiper-item-sp' : 'color')">
													<text class="swiper-item-title">油站价</text>
													<text class="swiper-item-desc">￥{{ item.gunPrice | moneyFt }}/L</text>
												</view>
												<view class="flex"><view class="u-border-left" style="height: 4vh;margin: auto;"></view></view>
												<view class="swiper-item-content-padding color flex-column">
													<text class="swiper-item-title">指导价</text>
													<text class="swiper-item-desc">￥{{ item.nationalStandardPrice | moneyFt }}/L</text>
												</view>
											</view>
										</swiper-item>
									</template>
								</swiper>
							</view>
						</view>
					</view>
					<view class="content flex-column">
						<!-- 油枪 -->
						<view class="flex-column" v-if="sysProduct !== 'NON_OIL'">
							<view class="flex-jc-between flex-end">
								<text class="bold-title">请选择油枪</text>
								<text style="font-size: 26rpx; line-height: 26rpx; margin: 1vh 4vw;color: #fd0000;">*请务必与加油员确认枪号或油品</text>
							</view>
							<view class="flex flex-wrap common-selector oilNo-selector">
								<template v-for="(item, index) in gunNoList">
									<view
										:class="'oilNo-item flex-column flex-center ' + (activeGunNo === item.gunNo ? 'select-active' : 'select-noactive')"
										@click="selectGunNo"
										:data-item="item"
										:key="index"
									>
										<text style="font-size: 36rpx; line-height: 36rpx; padding-bottom: 6rpx">{{ item.gunNo }}号</text>
										<text style="font-size: 26rpx; line-height: 26rpx; color: #999">{{ item.oilTypeDesc }}</text>
									</view>
								</template>
							</view>
						</view>

						<!-- 金额 -->
						<view class="flex-column">
							<text class="bold-title">{{ sysProduct !== 'NON_OIL' ? '请输入加油金额' : '请输入金额' }}</text>
							<view class="flex-center" v-if="sysProduct !== 'NON_OIL'">
								<view class="money-input">
									<view class="flex-center">
										<view @click="clickInput" :class="'money-input-item flex-start-center ' + (!isDisabled ? 'money-input-item-active' : '')">
											<view class="money-symbol"><text>￥</text></view>
											<view class="money-detail flex-center">
												<text class="placeholder-desc" v-if="!inputAmount">请填写加油金额</text>
												<text v-if="inputAmount">{{ inputAmount }}</text>
											</view>
											<view v-if="!isDisabled" :class="'custom-cursor flex-center ' + (inputAmount ? 'custom-cursor-active' : '')"></view>
											<view :class="'discount-detail flex-column flex-center ' + (!isDisabled ? 'discount-detail-active' : '')">
												<template v-if="activeGunNo !== -1">
													<text>{{ oilTypeDesc }}-{{ activeGunNo }}号枪</text>
													<u-line color="#999" margin="5rpx 0"></u-line>
												</template>
												<text>约{{ oilLiters | moneyFt }}L</text>
											</view>
										</view>
									</view>
								</view>
							</view>
							<view class="flex-center" v-if="sysProduct === 'NON_OIL'">
								<view class="money-input">
									<view class="flex-center">
										<view @click="clickInput" :class="'money-input-item flex-start-center ' + (!isDisabled ? 'money-input-item-active' : '')">
											<view class="money-detail flex-center">
												<text class="placeholder-desc" v-if="!inputAmount">请填写金额</text>
												<text v-if="inputAmount">{{ inputAmount }}</text>
											</view>
											<view v-if="!isDisabled" :class="'custom-cursor flex-center ' + (inputAmount ? 'custom-cursor-active' : '')"></view>
										</view>
									</view>
								</view>
							</view>
						</view>

						<!-- 全品类：非油金额框 -->
						<view class="flex-column" v-if="sysProduct === 'ALL' && showOtherInput">
							<text class="bold-title">请输入其他金额</text>
							<view class="money-input">
								<view class="flex-center">
									<view @click="clickOtherInput" :class="'money-input-item flex-start-center ' + (!isOtherDisabled ? 'money-input-item-active' : '')">
										<view class="money-symbol"><text>￥</text></view>
										<view class="money-detail flex-center">
											<text class="placeholder-desc" v-if="!otherAmount">请填写其他金额</text>
											<text v-if="otherAmount">{{ otherAmount }}</text>
										</view>
										<view v-if="!isOtherDisabled" :class="'custom-cursor flex-center ' + (otherAmount ? 'custom-cursor-active' : '')"></view>
									</view>
								</view>
							</view>
						</view>

						<!-- 支付方式 -->
						<view class="common-box" v-if="sysProduct !== 'NON_OIL' && !showKeyboard && showPayWay">
							<view class="common-box-item flex-column">
								<text class="common-box-item-title">选择支付方式</text>
								<u-radio-group v-model="payWay" @change="changePayWay" placement="column" iconPlacement="right" size="40" iconSize="30">
									<view class="flex-jc-between">
										<template v-if="provider==='weixin'">
											<u-icon label="微信支付" imgMode="widthFix" name="/static/image/public/wxpay.png"></u-icon>
										</template>
										<template v-else>
											<u-icon label="支付宝支付" imgMode="widthFix" name="/static/image/public/alipay.png"></u-icon>
										</template>
										<u-radio name="mini" activeColor="red"></u-radio>
									</view>
									<view class="flex-jc-between common-box-top">
										<u-icon label="加油卡" imgMode="widthFix" name="/static/image/checkstand/oil-card.png"></u-icon>
										<template v-if="!isNeedOpenCard">
											<text style="color: #fd0000;" @click="jumpToOilCard">点击开通</text>
										</template>
										<template v-else>
											<u-radio :label="'余额:￥' + oilCardAmount" labelSize="26" name="oilCard" activeColor="red"></u-radio>
										</template>
									</view>
								</u-radio-group>
							</view>
						</view>

						<!-- 积分抵扣 -->
						<view
							class="common-box"
							v-if="sysProduct !== 'NON_OIL' && showDiscount && !showKeyboard && openLightweightMember && payWay === 'mini' && isExisted && inputAmount"
						>
							<view class="common-box-item flex-column">
								<text class="common-box-item-title">会员优惠</text>
								<view class="flex-jc-between" @click="jumpToCoupon">
									<u-icon label="优惠券" imgMode="widthFix" name="/static/image/public/coupon.png"></u-icon>
									<text>
										<template v-if="useStatus === 0">
											暂无可用优惠券＞
										</template>
										<template v-else>
											<template v-if="discountAmount && discountAmount > 0">
												-￥{{ discountAmount }}＞
											</template>
											<template v-else>
												可使用优惠券{{ useNum }}张＞
											</template>
										</template>
									</text>
								</view>
								<view class="flex-jc-between common-box-top points-box" v-if="usedPointsResult && usedPointsResult.usablePoints" @click="clickCheck">
									<text>最多可用{{ usedPointsResult.usablePoints }}积分抵扣</text>
									<view class="flex-jc-between">
										<text>- ￥ {{ usedPointsResult.usablePointsDeductAmount | moneyFt }}</text>
										<u-radio-group v-model="checked" placement="column" iconPlacement="right" size="40" iconSize="30">
											<u-radio :name="true" activeColor="red"></u-radio>
										</u-radio-group>
									</view>
								</view>
							</view>
						</view>
					</view>
				</view>

				<!-- 定位锚点 -->
				<view id="amount-area"></view>

				<view class="submit-bar" v-if="!showKeyboard">
					<view class="submit-bar-content flex-center">
						<view class="submit-bar-info flex-jc-center">
							<view class="pay-money-box flex-column">
								<text>
									实付:￥
									<text style="font-size: 46rpx;line-height: 46rpx;">{{ orderFinalAmount }}</text>
								</text>
								<text v-if="actualMarketingAmount > 0" class="pay-money-box-discount">已优惠￥{{ actualMarketingAmount }}</text>
							</view>
							<view v-if="sysProduct !== 'NON_OIL' && orderFinalAmount > 0" class="pay-detail-box" @click="clickDetail">明细</view>
						</view>
						<u-button :customStyle="btnCustomStyle" :loading="submitLoading" loadingSize="40" loadingText="支付中" text="支付" @click="executePay"></u-button>
					</view>
					<view class="submit-bar-safe"></view>
				</view>
			</scroll-view>
		</view>

		<u-overlay :show="showOverlay" @click="clickHide" z-index="10">
			<view class="overlay-content">
				<view class="flex-jc-between flex-center">
					<text class="overlay-content-detail text-align-center">加油金额</text>
					<text class="line"></text>
					<text class="overlay-content-detail">{{ inputAmount | moneyFt }}元</text>
				</view>
				<view class="flex-jc-between flex-center">
					<text class="overlay-content-detail text-align-center">加油升数</text>
					<text class="line"></text>
					<text class="overlay-content-detail">约{{ oilLiters | moneyFt }}升</text>
				</view>
				<view class="flex-jc-between flex-center">
					<view class="overlay-content-detail text-align-center">优惠金额(含服务费)</view>
					<text class="line"></text>
					<text class="overlay-content-detail">{{ actualMarketingAmount | moneyFt }}元</text>
				</view>
				<view class="flex-jc-between flex-center">
					<text class="overlay-content-detail text-align-center">实付金额</text>
					<text class="line"></text>
					<text class="overlay-content-detail">{{ orderFinalAmount | moneyFt }}元</text>
				</view>
			</view>
		</u-overlay>

		<view v-if="openLightweightMember && showPhoneDialog">
			<my-authorization
				:mchNo="mchNo"
				:authStep="0"
				:filePath="filePath"
				:actualMarketingAmount="actualMarketingAmount"
				@closePhoneDialog="closePhoneDialog"
				@getPhoneNumber="getPhoneNumber"
			></my-authorization>
		</view>

		<view v-if="openLightweightMember && showPhoneDialogSp">
			<my-authorization :mchNo="mchNo" :authStep="1" :filePath="filePath" @closePhoneDialogSp="closePhoneDialogSp" @getPhoneNumber="getPhoneNumber"></my-authorization>
		</view>

		<u-overlay :z-index="9999" :show="showKeyboard" @click="closeKeyboard" custom-style="background-color: rgba(0,0,0,0)" />

		<view class="keyboard" v-if="showKeyboard">
			<my-keyboard
				:inputAmount="inputAmount"
				:isDisabled="isDisabled"
				:otherAmount="otherAmount"
				:isOtherDisabled="isOtherDisabled"
				@submit="closeKeyboard"
				@changeAmount="changeAmount"
			></my-keyboard>
		</view>
	</view>
</template>
<script>
import myAuthorization from '../../components/authorization/authorization';
import myKeyboard from '../../components/keyboard/keyboard';
import mySkeleton from '../../components/skeleton/skeleton';
import { formatOil } from '../../utils/util';
import { computeOrder, computeOrderForOil, consume } from '../../api/1-checkstand';
import { payType, payStatus, commontPayment } from '../../utils/commonPay';

const app = getApp();
export default {
	components: {
		myAuthorization,
		myKeyboard,
		mySkeleton
	},
	data() {
		return {
			btnCustomStyle: {
				width: '110px',
				height: '100%',
				borderRadius: 0,
				border: 'none',
				background: '#e70000',
				color: '#fff'
			},
			indicatorDots: false,
			autoplay: false,
			interval: 3500,
			duration: 500,
			vertical: true,
			circular: true, //swiper
			currIndex: 0,
			address: '',
			sysProduct: '', // OIL 油品类\NON_OIL 非油品类\ALL 全品类
			filePath: '',
			mchName: '',
			mchNo: '',
			oilList: [],
			gunNoList: [],
			activeGunNo: -1,
			isDisabled: true,
			isOtherDisabled: true,
			oilNo: '',
			oilTypeDesc: '',
			gunNo: '',
			selGunPrice: 0,
			selMemberPrice: 0,
			inputAmount: '',
			oilLiters: 0,
			otherAmount: '', //非油品金额（暂固定传0）
			usedPointsResult: null,
			directDiscount: 0, //直降金额
			platServiceFee: 0, //服务费
			actualMarketingAmount: 0, //最终优惠金额
			orderFinalAmount: 0, //最终订单金额
			oilCardAmount: 0, //油卡余额
			isExisted: false, // 是否会员
			openLightweightMember: false, //是否开通会员体系
			openFLowOilCardSave: false, //是否开通油卡
			canPay: false, //取消注册会员也能支付
			showPhoneDialog: false, //展示手机号授权框
			showPhoneDialogSp: false, //展示手机号授权框(提示特化版)
			showOtherInput: true, //展示非油输入选项
			showKeyboard: false, //展示键盘
			showSkeleton: true, //是否展示骨架屏
			showPayWay: false, //是否展示支付方式：微信支付/支付宝支付、油卡支付
			showDiscount: false, //是否展示优惠框
			isNeedOpenCard: false, //是否需要开卡
			checked: false, //是否勾选积分扣减
			submitLoading: false,
			showOverlay: false,
			originalResult: '',
			isNotMember: 0,
			toView: '',
			payWay: 'mini', // mini: 小程序支付 oilCard: 油卡
			provider: '',
			hasMemberDayDiscount: false, //是否有会员日折扣
			discount: '', // 会员日折扣
			useStatus: 0, // 是否有优惠劵,1表示有,0表示没有
			isUseCoupon: 1, // 是否使用优惠券,1表示使用,0表示不使用
			couponNo: '', // 可使用的券码
			discountAmount: 0, // 选用优惠劵金额
			useNum: 0 // 可使用优惠券张数
		};
	},
	/**
	 * 生命周期函数--监听页面加载
	 */
	onLoad: function(options) {
		const paymentCodeInfoDTO = app.globalData.userInfo.paymentCodeInfoDTO;
		if (paymentCodeInfoDTO) {
			// 设置基础信息
			this.setMchInfo();
		} else {
			app.globalData.eventBus.on('qryInfo', () => {
				// 设置基础信息
				this.setMchInfo();
			});
		}
	},
	/**
	 * 生命周期函数--监听页面初次渲染完成
	 */
	onReady: function() {},
	/**
	 * 生命周期函数--监听页面显示
	 */
	onShow: function() {
		this.autoplay = true;
	},
	/**
	 * 生命周期函数--监听页面隐藏
	 */
	onHide: function() {
		// 初始化部分数据
		this.initData();
	},
	/**
	 * 生命周期函数--监听页面卸载
	 */
	onUnload: function() {},
	/**
	 * 页面相关事件处理函数--监听用户下拉动作
	 */
	onPullDownRefresh: function() {},
	/**
	 * 页面上拉触底事件的处理函数
	 */
	onReachBottom: function() {},
	/**
	 * 用户点击右上角分享
	 */
	onShareAppMessage: function() {},
	methods: {
		setMchInfo: function() {
			const {
				address,
				filePath,
				mchName,
				mchNo,
				oilList,
				gunNoList,
				sysProduct,
				discount,
				openLightweightMember,
				hasMemberDayDiscount,
				openFLowOilCardSave
			} = app.globalData.userInfo.paymentCodeInfoDTO;
			const memberId = app.globalData.userInfo.memberId;
			
			this.showSkeleton = false;
			this.address = address;
			this.filePath = filePath;
			this.mchName = mchName;
			this.mchNo = mchNo;
			this.oilList = oilList;
			this.gunNoList = gunNoList;
			this.sysProduct = sysProduct;
			this.openLightweightMember = openLightweightMember;
			this.hasMemberDayDiscount = hasMemberDayDiscount;
			this.discount = discount;
			this.openFLowOilCardSave = openFLowOilCardSave;
			this.isExisted = memberId ? true : false;
			this.provider = app.globalData.provider;
		},

		changeSwiper: function(e) {
			this.currIndex = e.detail.current;
		},

		selectGunNo: function(e) {
			const { oilType, oilTypeDesc, gunNo, gunPrice, memberPrice } = e.currentTarget.dataset.item;
			let currIndex = this.oilList.findIndex(e => e.oilType === oilType);

			this.currIndex = currIndex;
			this.autoplay = false;
			this.oilNo = oilType;
			this.oilTypeDesc = oilTypeDesc;
			this.activeGunNo = gunNo;
			this.gunNo = gunNo;
			this.selGunPrice = gunPrice;
			this.selMemberPrice = memberPrice;
			this.inputAmount = '';
			this.isDisabled = false;
			this.isOtherDisabled = true;
			this.oilLiters = 0;
			this.directDiscount = 0;
			this.orderFinalAmount = 0;
			this.platServiceFee = 0;
			this.actualMarketingAmount = 0;
			this.checked = false;
			this.usedPointsResult = null;
			this.showPayWay = false;
			this.showKeyboard = true;

			const { oilCardAccount, paymentCodeInfoDTO } = app.globalData.userInfo;
			if (this.openFLowOilCardSave && oilCardAccount) {
				// 是否区分汽柴油：100 区分， 101 不区分
				if (paymentCodeInfoDTO.rechargeConfig.configType === 100) {
					this.oilCardAmount = formatOil(oilTypeDesc) === '1' ? oilCardAccount.petrolBalance : oilCardAccount.dieselBalance;
				} else {
					this.oilCardAmount = oilCardAccount.balance;
				}
			}
		},

		closeKeyboard: function() {
			this.showKeyboard = false;
			this.showPayWay = this.openFLowOilCardSave ? true : false;
			this.showDiscount = this.inputAmount ? true : false;
			this.isNeedOpenCard = app.globalData.userInfo.oilCardAccount ? true : false;
			this.toView = 'amount-area';

			// 需要重置为空，否则第二次锚点将失效
			setTimeout(() => {
				this.toView = '';
			}, 500);

			this.changePayWay();
		},

		changeAmount(event) {
			const { amount } = event.detail;
			if (!this.isDisabled) {
				this.inputAmount = amount;
			} else if (!this.isOtherDisabled) {
				this.otherAmount = amount;
			}

			this.checked = false;
			this.usedPointsResult = null;
			this.useStatus = 0;
			this.isUseCoupon = 1;
			this.couponNo = '';
			this.discountAmount = 0;
			this.useNum = 0;
			this.showPayWay = false;
		},

		changePayWay: function() {
			// 先判断支付方式
			if (this.payWay === 'oilCard') {
				this.checked = false;
				this.useStatus = 0;
				this.isUseCoupon = 1;
				this.couponNo = '';
				this.discountAmount = 0;
				this.useNum = 0;

				if (this.sysProduct === 'ALL') {
					// 油卡支付不支持非油品
					this.showOtherInput = false;
					this.isOtherDisabled = true;

					if (this.otherAmount) {
						uni.showToast({
							icon: 'none',
							title: '加油卡只支持加油金额支付。',
							duration: 2000
						});

						this.otherAmount = '';
					}
				}
			} else {
				if (this.sysProduct === 'ALL') {
					this.showOtherInput = true;
				}
			}

			this.computeFinal();
		},

		clickInput: function() {
			if ((this.sysProduct === 'OIL' || this.sysProduct === 'ALL') && !this.gunNo) {
				uni.showToast({
					title: '请先选择油枪',
					icon: 'error',
					duration: 2000
				});

				return;
			}
			this.isDisabled = false;
			this.showKeyboard = true;
			this.isOtherDisabled = true;
		},

		clickOtherInput: function() {
			this.isOtherDisabled = false;
			this.isDisabled = true;
			this.showKeyboard = true;
		},

		computeFinal: function() {
			return new Promise((resolve, reject) => {
				if (this.sysProduct === 'ALL') {
					// 输入金额
					if ((!this.inputAmount && !this.otherAmount) || (this.inputAmount <= 0 && this.otherAmount <= 0)) {
						this.clearData();

						reject('fail');
					} else {
						this.computeSel()
							.then(res => {
								resolve('success');
							})
							.catch(err => {
								reject('fail');
							});
					}
				} else if (this.sysProduct === 'OIL') {
					// 输入金额
					if ((!this.inputAmount && !this.otherAmount) || this.inputAmount <= 0) {
						this.clearData();

						reject('fail');
					} else {
						this.computeSel()
							.then(res => {
								resolve('success');
							})
							.catch(err => {
								reject('fail');
							});
					}
				} else {
					this.orderFinalAmount = this.inputAmount ? Number(this.inputAmount) : 0;
					this.otherAmount = this.inputAmount ? Number(this.inputAmount) : 0;

					resolve('success');
				}
			});
		},

		computeSel: function() {
			if (this.openFLowOilCardSave && this.payWay === 'oilCard') {
				return new Promise((resolve, reject) => {
					const reqParam = {
						consumeAmount: this.inputAmount,
						gunNo: this.activeGunNo,
						mchNo: this.mchNo
					};
					computeOrderForOil(reqParam)
						.then(res => {
							if (res.code === 200) {
								return res.data;
							} else {
								uni.showToast({
									title: res.msg,
									icon: 'error',
									duration: 2000
								});
								reject('fail');

								throw new Error(res.msg);
							}
						})
						.then(data => {
							const { consumeAmount, oilLiters } = data;
							this.originalResult = '';
							this.oilLiters = oilLiters;
							this.directDiscount = 0;
							this.orderFinalAmount = consumeAmount;
							this.usedPointsResult = null;
							this.platServiceFee = 0;
							this.actualMarketingAmount = 0;
							resolve('success');
						});
				});
			} else {
				return new Promise((resolve, reject) => {
					const reqParam = {
						oilAmount: this.inputAmount,
						gunNo: this.activeGunNo === -1 ? '' : this.activeGunNo,
						mchNo: this.mchNo,
						otherAmount: this.otherAmount,
						memberId: app.globalData.userInfo.memberId,
						isNotMember: this.isNotMember,
						isUseCoupon: this.isUseCoupon,
						isUseMemberDiscount: this.hasMemberDayDiscount ? 1 : 0,
						discount: this.discount,
						couponNo: this.couponNo
					};

					computeOrder(reqParam)
						.then(res => {
							if (res.code === 200) {
								return res.data;
							} else {
								uni.showToast({
									title: res.msg,
									icon: 'error',
									duration: 2000
								});
								reject('fail');

								throw new Error(res.msg);
							}
						})
						.then(data => {
							const {
								oilLiters,
								directDiscount,
								orderFinalAmount,
								usedPointsResult,
								platServiceFee,
								actualMarketingAmount,
								isUseCoupon,
								useStatus,
								couponNo,
								usableCouponDeductAmount
							} = data;

							this.originalResult = data;
							this.oilLiters = oilLiters;
							this.directDiscount = directDiscount;
							this.orderFinalAmount = orderFinalAmount;
							this.usedPointsResult = usedPointsResult;
							this.platServiceFee = platServiceFee;
							this.actualMarketingAmount = actualMarketingAmount;
							this.useStatus = useStatus ? useStatus : 0;
							this.isUseCoupon = isUseCoupon;
							this.couponNo = couponNo ? couponNo : '';
							this.discountAmount = usableCouponDeductAmount ? usableCouponDeductAmount : 0;

							resolve('success');
						});
				});
			}
		},

		beforeExecutePay: function() {
			return new Promise((resolve, reject) => {
				if (this.sysProduct === 'ALL') {
					if (this.inputAmount <= 0 && this.otherAmount <= 0) {
						uni.showToast({
							title: '请填写金额',
							icon: 'error',
							duration: 2000
						});
						reject();
					}
				} else if (this.sysProduct === 'OIL') {
					if (!this.gunNo) {
						uni.showToast({
							title: '请选择油枪号',
							icon: 'error',
							duration: 2000
						});
						reject();
					} else if (this.inputAmount <= 0) {
						console.log(this.inputAmount);
						uni.showToast({
							title: '请填写金额',
							icon: 'error',
							duration: 2000
						});
						reject();
					}
				} else {
					if (this.inputAmount <= 0) {
						uni.showToast({
							title: '请填写金额',
							icon: 'error',
							duration: 2000
						});
						reject();
					}
				}
				resolve();
			});
		},

		executePay: function() {
			console.log('wechat pay...');
			this.submitLoading = true;

			let params = {};
			const pcn = app.globalData.pcn;
			if (this.sysProduct !== 'NON_OIL') {
				if (this.openFLowOilCardSave && this.payWay === 'oilCard') {
					params = {
						type: payType.miniPay,
						mchNo: this.mchNo,
						paymentCodeNo: pcn,
						memberId: app.globalData.userInfo.memberId,
						oilsGunNo: this.gunNo,
						oilsSingleAmount: this.selGunPrice,
						oilsForecastLiters: this.oilLiters,
						consumeAmount: this.orderFinalAmount,
						oilsType: this.oilNo,
						orderFrom: this.provider === 'weixin' ? 1 : 2
					};
				} else {
					params = {
						type: payType.miniPay,
						mchNo: this.mchNo,
						gunNo: this.gunNo,
						oilType: this.oilNo,
						oilsSingleAmount: this.selGunPrice ? this.selGunPrice : 0,
						oilsDiscountAmount: this.selMemberPrice ? this.selMemberPrice : 0,
						oilsForecastLiters: this.checked ? this.usedPointsResult.oilLiters : this.oilLiters,
						oilsDirectDiscount: this.checked ? this.usedPointsResult.directDiscount : this.directDiscount,
						oilOriginalAmount: this.inputAmount ? this.inputAmount : 0,
						totalDiscountAmount: this.directDiscount,
						orderAmount: this.checked ? this.usedPointsResult.orderFinalAmount : this.orderFinalAmount,
						sysProduct: this.sysProduct,
						otherAmount: this.otherAmount,
						memberId: app.globalData.userInfo.memberId,
						points: this.checked ? this.usedPointsResult.usablePoints : 0,
						pointsDeductAmount: this.checked ? this.usedPointsResult.usablePointsDeductAmount : 0,
						platServiceFee: this.checked ? this.usedPointsResult.platServiceFee : this.platServiceFee,
						paymentCodeNo: pcn,
						isNotMember: this.isNotMember,
						payWay: this.provider === 'weixin' ? 'WEIXIN' : 'ALIPAY',
						isUseMemberDiscount: this.hasMemberDayDiscount ? 1 : 0,
						discount: this.discount,
						isUseCoupon: this.isUseCoupon,
						couponNo: this.couponNo,
						usableCouponDeductAmount: this.discountAmount
					};
				}
			} else {
				params = {
					type: payType.miniPay,
					mchNo: this.mchNo,
					oilsSingleAmount: 0,
					oilsDiscountAmount: 0,
					oilsForecastLiters: 0,
					oilsDirectDiscount: 0,
					oilOriginalAmount: 0,
					totalDiscountAmount: 0,
					orderAmount: this.checked ? this.usedPointsResult.orderFinalAmount : this.orderFinalAmount,
					sysProduct: this.sysProduct,
					otherAmount: this.otherAmount,
					memberId: app.globalData.userInfo.memberId,
					points: this.checked ? this.usedPointsResult.usablePoints : 0,
					pointsDeductAmount: this.checked ? this.usedPointsResult.usablePointsDeductAmount : 0,
					platServiceFee: this.checked ? this.usedPointsResult.platServiceFee : this.platServiceFee,
					paymentCodeNo: pcn,
					isNotMember: this.isNotMember,
					payWay: this.provider === 'weixin' ? 'WEIXIN' : 'ALIPAY'
				};
			}

			this.beforeExecutePay()
				.then(() => {
					// 先判断是否开通会员系统
					if (this.openLightweightMember) {
						// 会员存在则执行支付操作，不存在则执行授权注册操作
						let isExisted = this.isExisted;
						if (isExisted || this.canPay) {
							console.log('支付参数', params);

							uni.showLoading({
								title: '支付中',
								mask: true
							});

							// 是否开通油卡和选择油卡支付
							if (this.openFLowOilCardSave && this.payWay === 'oilCard') {
								// 油卡消费
								this.consumeOilCard(params);
							} else {
								this.finalPay(params);
							}
						} else {
							this.submitLoading = false;
							this.showPhoneDialog = true;
						}
					} else {
						console.log('支付参数', params);
						this.finalPay(params);
					}
				})
				.catch(err => {
					console.error(err);
					this.submitLoading = false;
				});
		},

		consumeOilCard: function(params) {
			const oilVariety = formatOil(this.oilTypeDesc);
			const orderFinalAmount = this.orderFinalAmount;

			consume(params)
				.then(res => {
					uni.hideLoading();
					this.submitLoading = false;

					if (res.code === 200) {
						return res.data;
					} else {
						this.insufficientBalance(res.msg);

						throw new Error(res.msg);
					}
				})
				.then(data => {
					this.clearData();

					if (data.status === 100) {
						this.jumpToTransactionResult(oilVariety, orderFinalAmount);
					} else {
						this.insufficientBalance(data.errMsg);
					}
				});
		},

		finalPay: function(params) {
			commontPayment(params).then(res=> {
				if(res.code === payStatus.success) {
					if (res.msg === 'requestPayment:ok') {
						console.log('支付成功');
						// #ifdef MP-WEIXIN
						this.jumpToCompletion(params.orderAmount, res.data.payOrderNo, res.data.orderCreateTime);
						// #endif
						// #ifdef MP-ALIPAY
						if (res.resultCode === '9000') {
							this.jumpToCompletion(params.orderAmount, res.data.payOrderNo, res.data.orderCreateTime);
						} else {
							uni.showToast({
								title: '您未完成支付',
								icon: 'error',
								mask: true,
								duration: 3000
							});
						}
						// #endif
					} else {
						this.jumpToCompletion(params.orderAmount, res.data.payOrderNo, res.data.orderCreateTime);
					}
				}
			}).catch((res)=> {
				if(res) {
					if(res.code === payStatus.cancel) {
						if (res.msg === 'requestPayment:fail cancel') {
							uni.showToast({
								title: '您已取消支付',
								icon: 'error',
								mask: true,
								duration: 3000
							});
						} else {
							uni.showToast({
								title: res.msg,
								icon: 'error',
								mask: true,
								duration: 3000
							});
						}
					} else if (res.code === payStatus.fail) {
						uni.showModal({
							title: '支付失败提示',
							content: res.data.errMsg || '支付请求失败',
							showCancel: false,
						
							success(res) {
								if (res.confirm) {
									console.log('用户点击确定');
								} else if (res.cancel) {
									console.log('用户点击取消');
								}
							}
						});
					}
				}
			}).finally(()=> {
				// #ifdef MP-WEIXIN
				wx.hideLoading({
					noConflict: true
				});
				// #endif
				
				// #ifdef MP-ALIPAY
				my.hideLoading();
				// #endif
				
				this.submitLoading = false;
				
				this.clearData();
			})
		},

		clickCheck() {
			let result = !this.checked;
			const dataSource = result ? this.usedPointsResult : this.originalResult;
			const { oilLiters, directDiscount, orderFinalAmount, platServiceFee, actualMarketingAmount } = dataSource;

			this.oilLiters = oilLiters;
			this.directDiscount = directDiscount;
			this.orderFinalAmount = orderFinalAmount;
			this.platServiceFee = platServiceFee;
			this.actualMarketingAmount = actualMarketingAmount;
			this.checked = result;
		},

		clickDetail: function() {
			this.showOverlay = !this.showOverlay;
		},

		clickHide: function() {
			this.showOverlay = false;
		},

		closePhoneDialog: function() {
			this.canPay = true;
			this.isNotMember = 1;
			this.showPhoneDialog = false;

			this.computeFinal();
		},

		closePhoneDialogSp: function() {
			this.showPhoneDialogSp = false;
		},

		getPhoneNumber: function(e) {
			const { isExisted } = e.detail;
			this.showPhoneDialog = false;
			this.showPhoneDialogSp = false;
			this.isExisted = isExisted;

			if (isExisted) {
				this.canPay = false;
			} else {
				this.canPay = true;
				this.isNotMember = 1;
			}

			this.computeFinal().then(res => {
				if (res === 'success') {
					this.executePay();
				}
			});
		},

		initData: function() {
			this.currIndex = 0;
			this.autoplay = false;
			this.oilNo = '';
			this.oilTypeDesc = '';
			this.activeGunNo = -1;
			this.gunNo = '';
			this.selGunPrice = 0;
			this.selMemberPrice = 0;
			this.inputAmount = '';
			this.isDisabled = true;
			this.isOtherDisabled = true;
			this.oilLiters = 0;
			this.otherAmount = '';
			this.directDiscount = 0;
			this.orderFinalAmount = 0;
			this.platServiceFee = 0;
			this.actualMarketingAmount = 0;
			this.checked = false;
			this.usedPointsResult = null;
			this.showPayWay = false;
			this.showDiscount = false;
			this.showKeyboard = false;
			this.showOtherInput = true;
			this.payWay = 'mini';
			this.useStatus = 0;
			this.isUseCoupon = 1;
			this.couponNo = '';
			this.discountAmount = 0;
			this.useNum = 0;
			this.isNeedOpenCard = false;
		},

		clearData: function() {
			this.inputAmount = '';
			this.oilLiters = 0;
			this.otherAmount = '';
			this.directDiscount = 0;
			this.orderFinalAmount = 0;
			this.platServiceFee = 0;
			this.actualMarketingAmount = 0;
			this.checked = false;
			this.usedPointsResult = null;
			this.useStatus = 0;
			this.isUseCoupon = 1;
			this.couponNo = '';
			this.discountAmount = 0;
			this.useNum = 0;
		},

		// 油卡余额不足处理逻辑
		insufficientBalance: function(msg) {
			let that = this;

			if (msg.indexOf('余额不足') != -1) {
				uni.showModal({
					title: '提示',
					confirmText: '去充值',
					content: msg,
					success(res) {
						if (res.confirm) {
							// 区分
							if (app.globalData.userInfo.paymentCodeInfoDTO.rechargeConfig.configType === 100) {
								// 柴油
								if (that.oilNo != 1) {
									that.jumpToRecharge(1);
								} else {
									that.jumpToRecharge(2);
								}
							} else {
								that.jumpToRecharge(0);
							}
						} else if (res.cancel) {
							console.log('用户点击取消');
						}
					}
				});
			} else {
				uni.showToast({
					title: msg,
					icon: 'error',
					duration: 2000
				});
			}
		},

		jumpToInfo: function() {
			let isExisted = this.isExisted;
			if (isExisted) {
				uni.navigateTo({
					url: '/packageM/member/member'
				});
			} else {
				this.showPhoneDialogSp = true;
			}
		},

		jumpToRecharge: function(oilVariety) {
			uni.navigateTo({
				url: '/packageM/member/oilCard/recharge/recharge?oilVariety=' + oilVariety
			});
		},

		jumpToOilCard: function() {
			uni.navigateTo({
				url: '/packageM/member/oilCard/oilCard'
			});
		},

		jumpToTransactionResult: function(oilVariety, orderFinalAmount) {
			uni.navigateTo({
				url: '/packageM/member/oilCard/transactionResult/transactionResult?oilVariety=' + oilVariety + '&orderFinalAmount=' + orderFinalAmount + '&mchName=' + this.mchName
			});
		},

		jumpToCompletion: function(orderAmount, payOrderNo, createTime) {
			uni.reLaunch({
				url: '/pages/completion/completion?mchName=' + this.mchName + '&orderAmount=' + orderAmount + '&payOrderNo=' + payOrderNo + '&createTime=' + createTime
			});
		},

		jumpToCoupon: function() {
			if (this.useStatus === 1) {
				uni.navigateTo({
					url: '/packageM/member/coupon/coupon?couponNo=' + this.couponNo + '&oilType=' + this.oilNo + '&gunNo=' + this.gunNo + '&orderAmount=' + this.inputAmount
				});
			}
		}
	}
};
</script>

<style scoped>
@import './checkstand.css';
</style>

<style lang="scss" scoped>
/deep/ .u-modal__content {
	padding: 0 !important;
}

/deep/ .u-modal__button-group--confirm-button {
	padding: 0 !important;
}

/deep/ .u-radio__text {
	margin-right: 8px;
}
</style>
