page {
    background-color: #fafafa;
}

.checkstand-header {
    background-color: #fafafa;
    border-radius: 20rpx;
    box-shadow: 0.125rem 0.25rem 0.375rem rgba(0, 0, 0, 0.1);
    padding: 1vh 4vw 2vh 4vw;
    margin: 0vh 2vw 2vh 2vw;
}

.checkstand-header-item {
    position: relative;
}

.checkstand-header-image {
    width: 18vw;
    height: 18vw;
    margin: 2vw;
    margin-right: 5vw;
    border-radius: 10rpx;
    border: 1px solid #ccc;
}

.member {
    position: absolute;
    top: 0;
    right: 0;
    font-size: 25rpx;
    color: #00bfbf;
}

.member-image {
    width: 8vw;
    height: 8vw;
}

.checkstand-header-info {
    padding: 1vh 0;
    justify-content: space-between;
}

.checkstand-header-title {
    font-size: 38rpx;
    line-height: 38rpx;
    font-weight: 600;
    color: #333;
    width: 45vw;
}

.current-title {
    width: 12vw;
    height: 12vw;
	color: #fd0000;
    font-size: 30rpx;
    font-weight: 800;
    line-height: 34rpx;
    letter-spacing: 2rpx;
    border: 5rpx solid #fd0000;
    border-radius: 15%;
    margin-right: 6vw;
}

.checkstand-prices-board {
    margin: 1vh 2vw 0 2vw;
    border-top: 1px solid #e2e2e2;
    padding-top: 1vh;
}

.swiper-item-content {
    height: 100%;
}

.swiper-item-content-padding {
    padding: 1vw 0;
    text-align: center;
	font-size: 28rpx;
}

.color-sp {
	color: #fd0000;
}

.swiper-item-title {
	padding-bottom: 1vh;
}

.swiper-item-sp {
	font-weight: 800;
}

.station-location {
    font-size: 22rpx;
    line-height: 30rpx;
}

.distance-text {
    font-size: 22rpx;
    line-height: 30rpx;
    color: #999;
}

.content {
    padding-bottom: 16vh;
}

.common-selector {
    transition: max-height 0.25s ease-in-out;
    transition: font-size 0.25s ease-in-out;
    font-size: 32rpx;
}

.oilNo-selector {
    padding: 0 2.5vw;
}

.money-selector {
    padding: 0 2vw;
}

.oilNo-item {
    margin: 1vh 1.3vw;
    padding: 1vw 2vw;
    border-radius: 10rpx;
    width: 16vw;
    text-align: center;
}

.select-active {
    background-color: rgba(252, 193, 0, 0.1);
    color: #ff9900;
    border: 1.6px solid #ff9900;
}

.select-noactive {
    color: #333;
    border: 1.6px solid #ccc;
}

.bold-title {
    margin: 1vh 2vw 1vh 4vw;
	font-size: 34rpx;
	line-height: 34rpx;
    font-weight: bolder;
}

.money-input {
    width: 100vw;
}

.money-input-item {
    position: relative;
    border: 1.6px solid #ccc;
    border-radius: 10rpx;
    margin: 1vh 0vw;
    width: 82vw;
    height: 8vh;
	padding-left: 9vw;
}

.money-input-item-active {
	border-color: #ff9900;
	background-color: rgba(252, 193, 0, 0.1);
}

.money-symbol {
	position: absolute;
	left: 2vw;
	font-size: 46rpx;
	font-weight: 800;
}

.money-detail {
	font-size: 46rpx;
	font-weight: 800;
}

.placeholder-desc {
	position: absolute;
	left: 9vw;
	width: 40vw;
    z-index: -99999;
	font-size: 40rpx;
    color: #c8c9cc;
}

.custom-cursor {
    position: relative;
}

.custom-cursor-active {
    margin-left: 2vw;
}

@keyframes blink {
    0%,
    100% {
        background-color: #ff9900;
    }

    50% {
        background-color: transparent;
    }
}

.custom-cursor::after {
    position: absolute;
    content: '';
    display: inline-block;
    width: 4.5rpx;
    height: 5vh;
    vertical-align: center;
    animation: blink 1.2s infinite steps(1, start);
}

.discount-detail {
	height: 100%;
	position: absolute;
	right: 0;
	border-left: 1.6px solid #ccc;
	padding: 0 3vw;
	font-size: 28rpx;
	font-weight: 600;
	color: #666;
}

.discount-detail-active {
	border-left-color: #ff9900;
}

.money-item {
	font-size: 36rpx;
    margin: 1vh 2vw;
    padding: 1vw 2vw;
    border-radius: 10rpx;
    width: 24vw;
    height: 5vh;
    text-align: center;
}

.preferential-price {
    font-size: 25rpx;
    color: #e70000;
}

.common-box {
    margin-top: 1.5vh;
}

.common-box-item {
    color: #333;
    font-size: 30rpx;
    border: 1.6px solid #ccc;
    border-radius: 10rpx;
    margin: 0 4vw;
    padding: 2vh 2vw;
}

.common-box-item-title {
    font-weight: bold;
    border-bottom: 1.6px solid #ccc;
    padding-bottom: 1vh;
    margin-bottom: 2vh;
}

.common-box-top {
	padding-top: 1vh;
	margin-top: 1vh;
	border-top: 1px dashed #ccc;
}


.points-box {
	position: relative;
}

.points-box::before {
	position: absolute;
	top: 0;
	left: 0;
	content: '';
	width: 100%;
	height: 100%;
	background-color: rgba(0, 0, 0, 0);
	z-index: 9999;
}

.custom-label {
    text-align: right;
    padding: 0;
    margin: 0;
}

.submit-bar {
	width: 100%;
	background: #FAFAFA;
	box-shadow: 0px 0px 16px 0px rgba(0, 0, 0, 0.17);
	border-top: 1px solid #ccc;
	background-color: #fff;
	position: fixed;
	-webkit-user-select: none;
	user-select: none;
	bottom: 0;
	left: 0;
	z-index: 100;
}

.submit-bar-content {
	height: 55px;
}

.submit-bar-info {
    flex: 1;
	padding-left: 15rpx;
}

.submit-bar-info .pay-money-box {
    font-size: 26rpx;
    font-weight: bold;
    color: #333;
	padding-left: 2vw;
}

.submit-bar-info .pay-money-box .pay-money-box-discount {
    color: #e70000;
}

.submit-bar-info .pay-detail-box {
    font-size: 30rpx;
    padding-right: 2vw;
    color: #666;
}

.submit-bar-safe {
	height: constant(safe-area-inset-bottom);
	height: env(safe-area-inset-bottom);
}

.overlay-content {
    position: absolute;
    bottom: calc(105rpx + env(safe-area-inset-bottom));
    width: 94vw;
    padding: 2vh 3vw;
    background-color: #aaaaaa;
    font-size: 30rpx;
    color: #fff;
}

.line {
	height: 2rpx;
	flex: 1;
    border-bottom: 1px #fff dashed;
}

.text-align-center {
    text-align: center;
}

.overlay-content-detail {
	padding: 10rpx 15rpx;
    font-size: 28rpx;
}

.auth-logo {
    width: 30vw;
    height: 30vw;
    border-radius: 50%;
    margin: 2vh 0;
}

.auth-desc {
    color: #666;
    font-size: 26rpx;
}

.keyboard {
    position: fixed;
    bottom: 0;
    z-index: 99999;
}

.keyboard-default {
    padding-bottom: 2vh;
}

@-webkit-keyframes myAnimation {
    from {
        bottom: 0;
    }

    to {
        bottom: 25vh;
    }
}

@keyframes myAnimation {
    from {
        bottom: 0;
    }

    to {
        bottom: 25vh;
    }
}

.start {
    position: fixed;
    -webkit-animation-name: myAnimation;
    animation-name: myAnimation;
    -webkit-animation-duration: 1s;
    animation-duration: 1s;
    -webkit-animation-fill-mode: forwards;
    animation-fill-mode: forwards;
}