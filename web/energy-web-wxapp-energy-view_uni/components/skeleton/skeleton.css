page {
    background-color: #fafafa;
}

.checkstand-header {
    background-color: #fafafa;
    border-radius: 20rpx;
    box-shadow: 0.125rem 0.25rem 0.375rem rgba(0, 0, 0, 0.1);
    padding: 1vh 4vw 2vh 4vw;
    margin: 0vh 2vw 2vh 2vw;
}

.checkstand-header-item {
    position: relative;
}

.checkstand-header-image {
    width: 18vw;
    height: 18vw;
    margin: 2vw;
    margin-right: 5vw;
    border-radius: 10rpx;
    border: 1px solid #ccc;
}

.checkstand-header-info {
    padding: 1vh 0;
    justify-content: space-between;
}

.bg-color {
	background-color: #EEEEEE;
}

.checkstand-header-title {
    width: 45vw;
	height: 3vh;
}

.distance-text {
    width: 45vw;
    height: 2vh;
}

.content {
    padding-bottom: 16vh;
}

.bold-title {
    margin: 1vh 4vw;
	width: 30vw;
	height: 4vh;
}

.money-input {
    width: 100vw;
}

.money-input-item {
    position: relative;
    border: 1px solid #ccc;
    border-radius: 10rpx;
    margin: 1vh 0vw;
    width: 92vw;
    height: 8vh;
}

.placeholder-desc {
	width: 40vw;
	height: 4vh;
}

.submit-bar {
	width: 100%;
	background: #FAFAFA;
	box-shadow: 0px 0px 16px 0px rgba(0, 0, 0, 0.17);
	border-top: 1px solid #ccc;
	background-color: #fff;
	position: fixed;
	-webkit-user-select: none;
	user-select: none;
	bottom: 0;
	left: 0;
	z-index: 100;
}

.submit-bar-content {
	height: 55px;
}