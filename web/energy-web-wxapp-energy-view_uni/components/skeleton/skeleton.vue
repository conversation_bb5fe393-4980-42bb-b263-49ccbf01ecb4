<template>
	<view v-if="show">
		<view :style="{
			width: width,
			height: height,
			backgroundColor: backgroundColor
		}">
			<view class="checkstand-header">
				<view class="flex checkstand-header-item">
					<image class="checkstand-header-image" src="/static/image/public/station-default-bg.png" mode="aspectFill" :lazy-load="false" />
					<view class="flex-column checkstand-header-info">
						<text class="checkstand-header-title bg-color"></text>
						<text class="distance-text bg-color"></text>
					</view>
				</view>
			</view>
			<view class="content flex-column">
				<!-- 金额 -->
				<view class="flex-column" id="amount-area">
					<text class="bold-title bg-color"></text>
					<view class="money-input">
						<view class="flex-center">
							<view class="money-input-item flex-center">
								<text class="placeholder-desc bg-color"></text>
							</view>
						</view>
					</view>
				</view>
			</view>
			<view class="submit-bar">
				<view class="submit-bar-content"></view>
			</view>
		</view>
	</view>
</template>

<script>
let systemInfo = uni.getSystemInfoSync();
export default {
	name: 'skeleton',
	props: {
	    show: {
	        type: Boolean,
	        default: true
	    }
	},
	data() {
		return {
			width: systemInfo.windowWidth + 'px',
			height: systemInfo.windowHeight + 'px',
			backgroundColor: '#fff'
		}
	}
}
</script>

<style scoped>
@import './skeleton.css';
</style>