/* components/authorization/authorization.wxss */

.auth-logo {
    width: 16vw;
    height: 16vw;
    border-radius: 50%;
    margin: 2vh 3vw;
}

.auth-money {
	font-size: 36rpx;
	font-weight: 800;
	color: #e70000;
	padding: 0 1vw;
}

.auth-desc {
    font-size: 32rpx;
	color: #333;
	text-align: center;
	padding-right: 3vw;
}

.auth-logo-sp {
    width: 30vw;
    height: 30vw;
    border-radius: 50%;
    margin: 2vh 0;
}

.auth-desc-sp {
    color: #666;
    font-size: 26rpx;
}

.auth-desc-sp::before {
    content: '*';
    padding-right: 1vw;
    color: #e70000;
}

.common-btn {
	flex: 1;
	display: flex;
	flex-direction: row;
	justify-content: center;
	align-items: center;
	height: 48px;
	font-size: 16px;
}

.auth-user-desc {
    margin: 2vh 0;
}
