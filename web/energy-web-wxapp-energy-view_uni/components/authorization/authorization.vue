<template>
    <view>
        <!-- 手机号授权弹窗 -->
		<u-modal class="custom-modal" title="温馨提示" :show="showPhone" :zoom="true">
			<view class="flex flex-center" slot="default">
				<view>
					<image class="auth-logo" :src="filePath ? filePath : '/static/image/public/station-default-bg.png'" mode="aspectFill" :lazy-load="false" />
				</view>
				<view class="auth-desc flex-column flex-center">
					<text>成为会员即可享受加油优惠</text>
					<text>本单可优惠<text class="auth-money">{{actualMarketingAmount}}</text>元！</text>
				</view>
			</view>
			<view class="flex u-border-top" slot="confirmButton">
				<button :disabled="showLoading" class="common-btn u-reset-button" style="color: #606266;" @click="closePhoneDialog" >否，原价付款</button>
				<view class="u-border-left"></view>
				<button :loading="showLoading" :disabled="showLoading" class="common-btn u-reset-button" style="color: #ee0a24;" open-type="getPhoneNumber" @getphonenumber="getPhoneNumber" >确定</button>
			</view>
		</u-modal>
		
		<u-modal class="custom-modal" title="温馨提示" :show="showPhoneSp" :zoom="true">
			<view class="flex-column flex-center" slot="default">
				<image class="auth-logo-sp" :src="filePath ? filePath : '/static/image/public/station-default-bg.png'" mode="aspectFill" :lazy-load="false" />
				<text class="auth-desc-sp">注册后即可享受会员优惠</text>
			</view>
			<view class="flex u-border-top" slot="confirmButton">
				<button :disabled="showLoading" class="common-btn u-reset-button" style="color: #606266;" @click="closePhoneDialogSp" >取消</button>
				<view class="u-border-left"></view>
				<button :loading="showLoading" :disabled="showLoading" class="common-btn u-reset-button" style="color: #ee0a24;" open-type="getPhoneNumber" @getphonenumber="getPhoneNumber" >确定</button>
			</view>
		</u-modal>

        <!-- 用户信息授权弹窗 -->
		<!-- #ifdef MP-ALIPAY -->
		<u-modal title="提示" :show="authStep === 2" confirmColor="#ee0a24" :zoom="true">
			<view class="flex-column flex-center">
			    <text class="auth-user-desc">请完善您的用户信息！</text>
			</view>
			<view class="flex u-border-top" slot="confirmButton">
				<button class="common-btn u-reset-button" style="color: #606266;" @click="closeUserDialog" >取消</button>
				<view class="u-border-left"></view>
				<button class="common-btn u-reset-button" style="color: #ee0a24;" open-type="getAuthorize" scope='userInfo' @getAuthorize="getAliUserInfo" >确定</button>
			</view>
		</u-modal>
		<!-- #endif -->
		
		<!-- #ifdef MP-WEIXIN -->
		<u-modal title="提示" :show="authStep === 2" confirmColor="#ee0a24" :zoom="true" showCancelButton @cancel="closeUserDialog" @confirm="getUserProfile">
			<view class="flex-column flex-center">
			    <text class="auth-user-desc">请完善您的用户信息！</text>
			</view>
		</u-modal>
		<!-- #endif -->
    </view>
</template>

<script>
// components/authorization/authorization.js
import { newUserRegister } from '../../api/1-checkstand';

let app = getApp();
export default {
    data() {
        return {
			showPhone: false,
			showPhoneSp: false,
			showLoading: false,
            isExisted: false // 用户是否存在
        };
    },
    /**
     * 组件的属性列表
     */
    props: {
        mchNo: '',
        filePath: '',//油站缩列图
        authStep: 0,
		actualMarketingAmount: 0 //优惠金额
    },
	watch: {
		authStep: {
			handler (newVal, oldVal) {
			    if(newVal === 0) {
					this.showPhone = true;
				} else if(newVal === 1) {
					this.showPhoneSp = true;
				}
			},
			immediate: true
		}
	},
    /**
     * 组件的方法列表
     */
    methods: {
        closePhoneDialog() {
            this.$emit('closePhoneDialog');
        },
		
		closePhoneDialogSp() {
		    this.$emit('closePhoneDialogSp');
		},

        closeUserDialog() {
            this.$emit('closeUserDialog');
        },

        getPhoneNumber: function (e) {
            const { errMsg, iv, encryptedData, sign, code } = e.detail;
			
            if (errMsg === 'getPhoneNumber:fail user deny') {
				if(this.authStep === 0) {
					this.closePhoneDialog();
				} else {
					this.closePhoneDialogSp();
				}
            } else if (errMsg === 'getPhoneNumber:ok') {
                const params = {
                    iv,
                    encryptedData,
					code,
                    mchNo: this.mchNo,
					type: app.globalData.provider === 'weixin' ? 1 : 2
                };
				
				this.register(params);
            } else {
                console.error(errMsg);
                this.$emit('getPhoneNumber', {
                    detail: {
                        isExisted: this.isExisted
                    }
                });
            }
        },
		
		register: function(params) {
			this.showLoading = true;
			
			newUserRegister(params).then((res) => {
				this.showLoading = false;
			    if (res.code === 200) {
			        this.isExisted = true;
						
			        return res.data;
			    } else {
			        uni.showToast({
			            title: res.msg,
			            duration: 2000
			        });
					
					throw new Error(res.msg);
			    }
			}).then((data) => {
			    if (data) {
					app.globalData.userInfo.memberId = data;
			    } else {
			        uni.showToast({
			            title: '请授权使用手机号',
			            icon: 'error',
			            duration: 2000
			        });
			    }
			
			    this.$emit('getPhoneNumber', {
			        detail: {
			            isExisted: this.isExisted
			        }
			    });
			});
		},
		
        getUserProfile: function (e) {
			wx.getUserProfile({
			    desc: '用于完善会员资料', // 声明获取用户个人信息后的用途，后续会展示在弹窗中，请谨慎填写
			    success: (res) => {
			        const { avatarUrl, nickName } = res.userInfo;
					this.$emit('getUserInfo', {
					    detail: {
					        avatarUrl: avatarUrl,
					        nickName: nickName
					    }
					});
			    },
				fail: (res) => {
					this.closeUserDialog();
				}
			})
        },
		
		getAliUserInfo: function(e) {
			my.getOpenUserInfo({
			    success: (res) => {
					let userInfo = JSON.parse(res.response).response;
					this.$emit('getUserInfo', {
					    detail: {
					        avatarUrl: userInfo.avatar ? userInfo.avatar : '',
					        nickName: userInfo.nickName ? userInfo.nickName : ''
					    }
					});
			    },
				fail: (res) => {
					console.log(res)
				}
			});
		}
    }
};
</script>

<style>
@import './authorization.css';
</style>
