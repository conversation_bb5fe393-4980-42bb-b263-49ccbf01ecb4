<template>
    <view class="plate-keyboard-container">
        <view class="keyboard-title">请输入金额</view>
        <view class="keyboard-content">
            <view class="keyboard-panel flex-wrap">
                <block v-for="(item, index) in keyNumber" :key="index">
                    <view class="key-panel" @tap.stop.prevent="onKeyboardTap" :data-value="item">{{ item }}</view>
                </block>
                <view class="flex-wrap">
                    <view class="key-zero" @tap.stop.prevent="onKeyboardTap" data-value="0">0</view>
                    <view class="key-symbol" @tap.stop.prevent="onKeyboardTap" data-value=".">.</view>
                </view>
            </view>
            <view class="keyboard-btn">
                <view class="keyboard-del flex-center" @tap.stop.prevent="onKeyboardTap" data-value="delete">
                    <image src="/static/image/keyboard/icon-del.png" :lazy-load="false" />
                </view>
                <view class="keyboard-confirm" @tap.stop.prevent="submit">确定</view>
            </view>
        </view>
    </view>
</template>

<script>
export default {
    data() {
        return {
            keyNumber: '123456789',
            //是否显示键盘,默认显示
            inputPlate: {
                plates: [] //接收输入的内容
            }
        };
    },
    /**
     * 组件的属性列表
     */
    props: {
        // 这里定义了innerText属性，属性值可以在组件使用时指定
        inputAmount: null,
		isDisabled: null,
		otherAmount: null,
		isOtherDisabled: null,
    },
    /**
     * 组件的方法列表
     */
    methods: {
        //点击键盘事件
        onKeyboardTap: function (event) {
			// 油品金额
			if(!this.isDisabled) {
				let inputAmount = this.inputAmount;
				if (inputAmount) {
				    inputAmount = inputAmount + '';
				    this.inputPlate.plates = [];
				    let size = inputAmount.length;
				
				    for (let i = 0; i < size; i++) {
				        if (i < size - 1) {
				            this.inputPlate.plates.push(inputAmount.substring(i, i + 1));
				        } else {
				            this.inputPlate.plates.push(inputAmount.substring(size - 1));
				        }
				    }
				}
			}
            
			// 非油品金额（特指输入框为非油框）
			if(!this.isOtherDisabled) {
				let otherAmount = this.otherAmount;
				if (otherAmount) {
				    otherAmount = otherAmount + '';
				    this.inputPlate.plates = [];
				    let size = otherAmount.length;
				
				    for (let i = 0; i < size; i++) {
				        if (i < size - 1) {
				            this.inputPlate.plates.push(otherAmount.substring(i, i + 1));
				        } else {
				            this.inputPlate.plates.push(otherAmount.substring(size - 1));
				        }
				    }
				}
			}

            const { value } = event.currentTarget.dataset;
            switch (value) {
                case 'delete':
                    if (this.inputPlate.plates.length > 0) {
                        this.inputPlate.plates.pop();
                    }
                    break;
                case '.':
                    if (this.inputPlate.plates.indexOf('.') > -1) {
                        break;
                    }
                default:
                    if (this.inputPlate.plates.length === 7) {
                        break;
                    }
                    if (this.inputPlate.plates.indexOf('.') > -1) {
                        const symbolIndex = this.inputPlate.plates.indexOf('.');
                        const arrayLen = this.inputPlate.plates.length;

                        if (arrayLen - 1 - symbolIndex === 2) {
                            break;
                        }
                    }
                    this.inputPlate.plates.push(value);
            }
 
            this.changeAmount();
        },
		
        //提交事件
        submit: function () {
            this.$emit('submit');
        },
		
        changeAmount: function () {
            //数组组装字符串
            let plates = JSON.parse(JSON.stringify(this.inputPlate.plates)); // 实现数组的深拷贝

            const plate = plates.join('');
            this.$emit('changeAmount', {
                detail: {
                    amount: plate
                }
            });
        }
    }
};
</script>
<style>
@import './keyboard.css';
</style>
