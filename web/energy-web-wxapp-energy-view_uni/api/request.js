import { login } from '../utils/login';

// const BASE_URL = 'https://test.jayocun.com/dev/payapi';  // 开发环境
// const BASE_URL = 'https://test.jayocun.com/test/payapi'; // 测试环境
const BASE_URL = 'https://wx.jayocun.com'; // 生产环境

const { token } = uni.getStorageSync('baseInfo');
let accessToken = token;
class Request {
    constructor(options) {
        this._header = { ...options };
    }

    setErrorHandler(handler) {
        this._errorHandler = handler;
    }

	// 必须是wxRequest，不要改这个名字
    wxRequest({ url, data, header, method, callback = '' }) {
		let that = this;
		
        try {
			if (accessToken) {
				header = Object.assign({
					accessToken: accessToken
				});
			}
        } catch (e) {
            console.error(e);
        }
		
        return new Promise((resolve, reject) => {
            uni.request({
                url: BASE_URL + url,
                data,
                header,
                method,
                success: (res) => {
					if (callback) {
						return callback(res.data);
					}
					
                    if (res.statusCode === 200) {
						if (res.data.code === 300) {
						    if(res.data.msg === 'Token失效' || res.data.msg === 'Token不能为空') {
								// 登录
								login().then(res => {
									const { provider, openId, token } = res;
									
									// 这里针对第一次登陆qryBaseInfo接口没有相关信息，回调参数重新设值
									if(url.includes("baseInfo")) {
										data.openId = openId;
										data.type = provider.includes('weixin') ? 1 : 2;
									}
									
									// 设置token
									accessToken = token;
									that.wxRequest({url, data, method, callback: resolve})
								})
						    } else {
								resolve(res.data);
							}
						} else {
							resolve(res.data);
						}
                    } else {
                        if (this._errorHandler !== null) {
                            console.log('request: ', res);
                        }

                        reject(res.data);
                    }
                },
                fail: (res) => {
                    if (this._errorHandler !== null) {
                        console.log('request: ', res);
                    }

                    reject(res);
                }
            });
        });
    }
}

const request = new Request();
export default request;
