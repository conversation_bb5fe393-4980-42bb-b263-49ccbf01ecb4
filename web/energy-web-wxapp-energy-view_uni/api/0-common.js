import request from './request'; 

// 查询会员油卡账户
export const qryFlowOilCardAccount = (data) => {
    return request.wxRequest({
        url: '/miniApi/paymentCode/oilCard/qryFlowOilCardAccount',
        method: 'POST',
		data
    });
};

// 查询基础信息（聚合收款码和会员）
export const qryBaseInfo = (data) => {
    return request.wxRequest({
        url: '/miniApi/paymentCode/auth/baseInfo',
        method: 'POST',
		data
    });
};

// 支付
export const commonPay = (url, data) => {
    return request.wxRequest({
        url: url,
        method: 'POST',
        data
    });
};