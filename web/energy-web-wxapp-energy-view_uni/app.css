@import './style/iconfont.css'; /**app.wxss**/

.container {
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    padding: 200rpx 0;
    box-sizing: border-box;
}
.bold {
	font-weight: 800;
}
.fz-26 {
	font-size: 26rpx;
}
.fz-30 {
	font-size: 30rpx;
}
.fz-40 {
	font-size: 40rpx;
}
.fz-45 {
	font-size: 45rpx;
}
.fz-60 {
	font-size: 60rpx;
}
.flex {
    display: flex;
}
.flex-column {
    display: flex;
    flex-direction: column;
}
.flex-center {
    display: flex;
    justify-content: center;
    align-items: center;
}
.flex-start-center {
    display: flex;
    justify-content: flex-start;
    align-items: center;
}
.flex-start-end {
    display: flex;
    justify-content: flex-start;
    align-items: flex-end;
}
.flex-jc-between {
    display: flex;
    justify-content: space-between;
}
.flex-jc-around {
    display: flex;
    justify-content: space-around;
}
.flex-end {
    display: flex;
    align-items: flex-end;
}
.flex-end-end {
    display: flex;
    align-items: flex-end;
    justify-content: flex-end;
}
.flex-wrap {
    display: flex;
    flex-wrap: wrap;
}
.flex-jc-center {
    display: flex;
    justify-content: space-between;
	align-items: center;
}
.flex-column-center {
	display: flex;
	flex-direction: column;
	justify-content: center;
}
