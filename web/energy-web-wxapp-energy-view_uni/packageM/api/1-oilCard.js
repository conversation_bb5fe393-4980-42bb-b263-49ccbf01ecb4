import request from '../../api/request'; 

// 开卡
export const openOilCardAccount = (data) => {
    return request.wxRequest({
        url: '/miniApi/paymentCode/oilCard/openOilCardAccount',
        method: 'POST',
        data
    });
};

// 查看商户充值配置信息
export const qryRechargeConfig = (data) => {
    return request.wxRequest({
        url: '/miniApi/paymentCode/oilCard/qryRechargeConfig',
        method: 'POST',
		data
    });
};

// 油卡交易记录查询
export const queryOrder = (data) => {
    return request.wxRequest({
        url: '/miniApi/paymentCode/oilCard/queryOrder',
        method: 'POST',
		data
    });
};

// 查询会员充值和消费统计
export const getTransactions = (data) => {
    return request.wxRequest({
        url: '/miniApi/paymentCode/oilCard/count',
        method: 'POST',
		data
    });
};

// 油卡交易记录详情
export const queryOrderDetail = (data) => {
    return request.wxRequest({
        url: '/miniApi/paymentCode/oilCard/queryOrderDetail',
        method: 'POST',
		data
    });
};