page {
    background-color: #F3F5F7;
}

.empty {
    width: 25vw;
}

.coupon-container {
	width: 100vw;
	min-height: 100vh;
}

.coupon-content {
	flex: 1;
}

.title {
	font-size: 30rpx;
	color: #333;
	padding: 2vh 0 2vh 6vw;
}

.common-box {
	background-color: #fff;
	margin: 0 4vw 2vh 4vw;
	padding: 2vh 4vw;
	border-radius: 15rpx;
	border: 2px solid #fff;
	position: relative;
}

.common-box::before {
	position: absolute;
	top: 0;
	left: 0;
	content: '';
	width: 100%;
	height: 100%;
	background-color: rgba(0, 0, 0, 0);
	z-index: 9999;
}

.common-box text {
	padding: 5rpx 0;
}

.color-default {
	color: #333;
}

.color-noactive {
	color: #999;
}

.box-border-active {
	border: 2px solid #ff9900;
}

.coutom-radio {
	margin-left: 2vw;
	padding-top: 5rpx;
}

.coupon-footer {
	display: flex;
	align-items: center;
	width: 100vw;
	height: 7vh;
	color: #ff9900;
}

.submit-bar {
	width: 100%;
	background: #FAFAFA;
	box-shadow: 0px 0px 16px 0px rgba(0, 0, 0, 0.17);
	border-top: 1px solid #ccc;
	background-color: #fff;
	position: fixed;
	-webkit-user-select: none;
	user-select: none;
	bottom: 0;
	left: 0;
	z-index: 99999;
}

.submit-bar-content {
	height: 55px;
}

.submit-bar-info {
    flex: 1;
	padding-left: 20rpx;
}

.submit-bar-safe {
	height: constant(safe-area-inset-bottom);
	height: env(safe-area-inset-bottom);
}