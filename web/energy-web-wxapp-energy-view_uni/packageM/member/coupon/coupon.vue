<template>
	<view class="coupon-container flex-column">
		<view class="coupon-content flex-column">
			<text class="title">可使用优惠券&nbsp;{{useNum}}张</text>
			<u-radio-group v-if="couponList.length > 0" v-model="couponSel" placement="column" iconPlacement="right"
				size="40" iconSize="30">
				<template v-for="(item,index) in couponList">
					<view @click="changeCoupon(item)" :class="'common-box ' + (item.isUse!==0 ? 'color-default ' : 'color-noactive ' ) + (couponSel === item.receiveNo ? 'box-border-active' : '')" :key="index" :style="(showButton&&index===(couponList.length-1)) ? 'margin-bottom: 15vh' : ''">
						<view class="flex-jc-between">
							<view class="flex-column">
								<text class="fz-40 bold">{{item.couponName}}</text>
								<text class="fz-26">有效期至：{{item.endTime}}</text>
							</view>
							<view class="flex">
								<view class="flex-column flex-end-end">
									<text class="fz-40 bold">￥{{item.couponAmount}}</text>
									<text class="fz-26">满{{item.thresholdAmount}}可用</text>
								</view>
								<view class="coutom-radio" v-if="showButton">
									<u-radio :name="item.receiveNo" activeColor="#ff9900" :disabled="item.isUse===0"></u-radio>
								</view>
							</view>	
						</view>
						<u-divider :dashed="true" lineColor="#b6b6b6"></u-divider>
						<view>
							<text class="fz-26">{{item.oilUseDesc}}</text>
						</view>
					</view>
				</template>
			</u-radio-group>
			<view v-else class="flex-center" style="flex: 1;">
			    <image class="empty" mode="widthFix" src="../../static/integral/empty.png" />
			</view>
		</view>
		<view v-if="!showButton" class="coupon-footer flex-column coupon-footer-height-7">
			<text @click="jumpToExpired">查看已失效的券＞</text>
		</view>
		<view class="submit-bar" v-if="showButton">
			<view class="submit-bar-content flex-center">
				<view class="submit-bar-info flex-jc-center">
					<view class="pay-money-box flex-column">
						<text>优惠 ￥{{couponSelAmount}}</text>
					</view>
				</view>
				<u-button :customStyle="btnCustomStyle" text="确定" @click="jumpToCheckStand()"></u-button>
			</view>
			<view class="submit-bar-safe"></view>
		</view>
	</view>
</template>

<script>
import {getCouponList} from '../../api/3-coupon'		

const app = getApp();	
export default {
	data() {
		return {
			btnCustomStyle: {
				width: '110px',
				height: '100%',
				borderRadius: 0,
				border: 'none',
				background: '#2979FF',
				color: '#fff'
			},
			useNum: 0,
			couponList: [],
			showButton: false,
			couponSel: '',
			couponSelAmount: 0,
			oilType: '', // 油品类型,支付时查询可使用优惠劵传入
			gunNo: '', // 油枪,支付时查询可使用优惠劵传入
			orderAmount: '' // 油品金额,用于支付时选取优惠劵,判断是否可用
		}
	},
	/**
	 * 生命周期函数--监听页面加载
	 */
	onLoad: function(options) {
		if(Object.keys(options).length !== 0) {
			const { couponNo, oilType, gunNo, orderAmount } = options;
			this.couponSel = couponNo;
			this.oilType = oilType;
			this.gunNo = gunNo;
			this.orderAmount = orderAmount;
			this.showButton = true;
		}
	},
	/**
	 * 生命周期函数--监听页面初次渲染完成
	 */
	onReady: function() {},
	/**
	 * 生命周期函数--监听页面显示
	 */
	onShow: function() {
		this.getCouponList();
	},
	/**
	 * 生命周期函数--监听页面隐藏
	 */
	onHide: function() {
	
	},
	/**
	 * 生命周期函数--监听页面卸载
	 */
	onUnload: function() {},
	/**
	 * 页面相关事件处理函数--监听用户下拉动作
	 */
	onPullDownRefresh: function() {},
	/**
	 * 页面上拉触底事件的处理函数
	 */
	onReachBottom: function() {},
	/**
	 * 用户点击右上角分享
	 */
	onShareAppMessage: function() {},
	methods: {
		// 100: "可使用", 101: "已使用",102: "已失效", 103: "已撤销"
		getCouponList: function() {
			let params = {
				memberId:  app.globalData.userInfo.memberId,
				mchNo: app.globalData.userInfo.paymentCodeInfoDTO.mchNo,
				status: 100,
				oilType: this.oilType,
				gunNo: this.gunNo,
				orderAmount: this.orderAmount
			}
			
			getCouponList(params).then(res=> {
				if (res.code === 200) {
					const {useNum, couponList} = res.data;
					this.useNum = useNum;
					this.couponList = couponList;
					let item = couponList.find(item=> item.receiveNo == this.couponSel);
					if(item) {
						this.couponSelAmount = item.couponAmount;
					}			
				} else {
					uni.showToast({
						title: res.msg,
						icon: 'error',
						duration: 2000
					});
				}
			})
		},
		changeCoupon: function(item) {
			if(this.showButton && item.isUse===1) {
				let couponSel = this.couponSel;
				this.couponSel = item.receiveNo === couponSel ? '' : item.receiveNo;
				this.couponSelAmount = item.receiveNo === couponSel ? 0 : item.couponAmount;
			}
		},
		jumpToCheckStand: function() {
			let pages = getCurrentPages(); // 当前页，
			let prevPage = pages[pages.length - 2]; // 上一页
			prevPage.$vm.couponNo = this.couponSel;
			prevPage.$vm.useNum = this.useNum;
			prevPage.$vm.checked = false;
			prevPage.$vm.isUseCoupon = this.couponSel ? 1 : 0; 
			prevPage.$vm.computeFinal();
			
			uni.navigateBack();
		},
		jumpToExpired: function() {
			uni.navigateTo({
				url: "/packageM/member/coupon/expired/expired"
			})
		}
	}
}
</script>

<style>
@import './coupon.css';
</style>