<template>
	<view class="expired-container flex-column">
		<template v-for="(item,index) in couponList">
			<view class="common-box" :key="index">
				<view class="flex-jc-between">
					<view class="flex-column">
						<text class="fz-40 bold">{{item.couponName}}</text>
						<text class="fz-26">有效期至：{{item.endTime}}</text>
					</view>
					<view class="flex">
						<view class="flex-column flex-end-end">
							<text class="fz-40 bold">￥ {{item.couponAmount}}</text>
							<text class="fz-26">满{{item.thresholdAmount}}可用</text>
						</view>
					</view>	
				</view>
				<u-divider :dashed="true" lineColor="#b6b6b6"></u-divider>
				<view class="flex-jc-between">
					<text class="fz-26">{{item.oilUseDesc}}</text>
					<text class="fz-30 bold" style="color: #333;">{{item.status | lightWeightPriorityUseStatusFt}}</text>
				</view>
			</view>
		</template>
		<template v-if="couponList.length === 0">
			<div class="flex-center" style="flex: 1;">
				<image class="empty" mode="widthFix" src="../../../static/integral/empty.png" />
			</div>
		</template>
	</view>
</template>

<script>
import {getCouponList} from '../../../api/3-coupon'		

const app = getApp();	
export default {
	data() {
		return {
			useNum: 0,
			couponList: [],
		}
	},
	/**
	 * 生命周期函数--监听页面加载
	 */
	onLoad: function(options) {
	},
	/**
	 * 生命周期函数--监听页面初次渲染完成
	 */
	onReady: function() {},
	/**
	 * 生命周期函数--监听页面显示
	 */
	onShow: function() {
		this.getCouponList();
	},
	/**
	 * 生命周期函数--监听页面隐藏
	 */
	onHide: function() {
	
	},
	/**
	 * 生命周期函数--监听页面卸载
	 */
	onUnload: function() {},
	/**
	 * 页面相关事件处理函数--监听用户下拉动作
	 */
	onPullDownRefresh: function() {},
	/**
	 * 页面上拉触底事件的处理函数
	 */
	onReachBottom: function() {},
	/**
	 * 用户点击右上角分享
	 */
	onShareAppMessage: function() {},
	methods: {
		// 100: "可使用", 101: "已使用",102: "已失效", 103: "已撤销"
		getCouponList: function() {
			let params = {
				memberId:  app.globalData.userInfo.memberId,
				mchNo: app.globalData.userInfo.paymentCodeInfoDTO.mchNo,
				statusList: ['101', '102', '103']
			}
			
			getCouponList(params).then(res=> {
				if (res.code === 200) {
					const {useNum, couponList} = res.data;
					this.useNum = useNum;
					this.couponList = couponList;
				} else {
					uni.showToast({
						title: res.msg,
						icon: 'error',
						duration: 2000
					});
				}
			})
		}
	}
}
</script>

<style>
@import './expired.css'
</style>