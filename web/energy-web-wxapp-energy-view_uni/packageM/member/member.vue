<template>
	<view>
		<view class="member-header">
			<view class="member-header-user flex">
				<image class="header-img" :src="headImgUrl ? headImgUrl : '../static/member/default-header.png'" mode="aspectFill" :lazy-load="false" />
				<view class="user-desc flex-column-center">
					<!-- #ifdef MP-WEIXIN -->
					<text>{{ wxNickName ? wxNickName : '微信用户' }}</text>
					<!-- #endif -->
					<!-- #ifdef MP-ALIPAY -->
					<text>{{ wxNickName ? wxNickName : '支付宝用户' }}</text>
					<!-- #endif -->
					<text class="user-desc-phone">{{ phoneNo }}</text>
				</view>
			</view>
			<view class="member-header-detail">
				<image class="navigation-img" mode="aspectFill" src="../static/member/navigation-bg.png" />
				<view class="member-header-detail-item flex-jc-between">
					<view class="flex-column flex-center" style="flex: 1;" @click="jumpCommon('/packageM/member/integral/integral', true)">
						<text class="member-header-detail-item-desc">{{ points }}</text>
						<text class="member-header-detail-item-title">积分余额</text>
					</view>
					<view class="flex-column flex-center" style="flex: 1;" @click="jumpCommon('/packageM/member/coupon/coupon', openMemberMarketingProduct)">
						<text class="member-header-detail-item-desc">{{ useNum }}</text>
						<text class="member-header-detail-item-title">优惠券(张)</text>
					</view>
					<view class="flex-column flex-center" style="flex: 1;" @click="jumpCommon('/packageM/member/oilCard/oilCard', openFLowOilCardSave)">
						<text class="member-header-detail-item-desc">{{ openFLowOilCardSave ? '￥' + oilBalance : '未开通' }}</text>
						<text class="member-header-detail-item-title">油卡余额</text>
					</view>
				</view>
			</view>
		</view>
		<view class="member-content">
			<view class="member-content-title"><text>我的服务</text></view>
			<view class="flex-wrap member-content-entrance">
				<template v-for="(item, index) in menus">
					<view
						v-show="item.show"
						:class="(menusActiveLength > 8 ? 'member-content-entrance-item' : 'member-content-entrance-item-bigger') + ' flex-column flex-jc-center'"
						@click="jumpCommon(item.value, item.allow)"
						:key="item.value"
					>
						<image class="content-img" :src="item.logo"></image>
						<text>{{ item.title }}</text>
					</view>
				</template>
			</view>
		</view>

		<view v-if="showPhoneDialog">
			<my-authorization :mchNo="mchNo" :authStep="1" :filePath="filePath" @closePhoneDialogSp="closePhoneDialogSp" @getPhoneNumber="getPhoneNumber"></my-authorization>
		</view>
	</view>
</template>

<script>
import myAuthorization from '../../components/authorization/authorization';
import { getInfo } from '../api/0-member';

const app = getApp();
const global = {
	fromGzh: false
};
export default {
	components: {
		myAuthorization
	},
	data() {
		return {
			mchNo: '',
			filePath: '',
			openLightWeightMemberStandard: false,
			openFLowOilCardSave: false,
			openMemberMarketingProduct: false,
			showPhoneDialog: false,
			memberId: '',
			wxNickName: '',
			headImgUrl: '',
			phoneNo: '',
			points: 0,
			useNum: 0,
			oilBalance: 0,
			menus: [
				{ logo: '../static/member/checkstand.png', title: '加油缴费', value: '/pages/checkstand/checkstand', allow: true, show: true },
				{ logo: '../static/member/navigation.png', title: '油站导航', value: '', allow: false, show: true },
				{ logo: '../static/member/refueling-record.png', title: '加油记录', value: '/packageM/member/refuelingRecord/refuelingRecord', allow: true, show: true },
				{ logo: '../static/member/oil-price.png', title: '今日油价', value: '', allow: false, show: true },
				{ logo: '../static/member/integral-record.png', title: '我的积分', value: '/packageM/member/integral/integral', allow: true, show: true },
				{ logo: '../static/member/oil-card.png', title: '我的油卡', value: '/packageM/member/oilCard/oilCard', allow: false, show: false },
				{ logo: '../static/member/coupon.png', title: '优惠券包', value: '/packageM/member/coupon/coupon', allow: false, show: false },
				{ logo: '../static/member/special-car.png', title: '专车优惠', value: '', allow: false, show: false },
				{ logo: '../static/member/member.png', title: '会员信息', value: '', allow: false, show: true },
				{ logo: '../static/member/newcomer-award.png', title: '新人有礼', value: '', allow: false, show: false },
				{ logo: '../static/member/member-day.png', title: '会员日', value: '', allow: false, show: false },
				{ logo: '../static/member/points-mall.png', title: '积分商城', value: '', allow: false, show: false }
			],
			menusActiveLength: 0,
			pointsDetails: []
		};
	},
	watch: {
		openLightWeightMemberStandard(newValue, oldValue) {
			if (newValue) {
				this.menus.forEach(item => {
					if (
						item.title === '优惠券包' ||
						item.title === '新人有礼' ||
						item.title === '会员日' ||
						item.title === '积分商城' ||
						item.title === '我的油卡' ||
						item.title === '专车优惠'
					) {
						item.show = true;

						this.menusActiveLength++;
					}
				});
			}
		},
		openFLowOilCardSave(newValue, oldValue) {
			if (newValue) {
				this.menus.forEach(item => {
					if (item.title === '我的油卡') {
						item.allow = true;
					}
				});
			}
		},
		openMemberMarketingProduct(newValue, oldValue) {
			if (newValue) {
				this.menus.forEach(item => {
					if (item.title === '优惠券包' || item.title === '新人有礼' || item.title === '会员日' || item.title === '积分商城') {
						if (item.value) {
							item.allow = true;
						}
					}
				});
			}
		}
	},
	/**
	 * 生命周期函数--监听页面加载
	 */
	onLoad: function(options) {
		console.log(options, 'onload');
		if (options.scene) {
			global.fromGzh = true;
		}
		
		// 初始化
		this.menusActiveLength = this.menus.filter(item => item.show === true).length;
	},
	/**
	 * 生命周期函数--监听页面初次渲染完成
	 */
	onReady: function() {},
	/**
	 * 生命周期函数--监听页面显示
	 */
	onShow: function() {
		const paymentCodeInfoDTO = app.globalData.userInfo.paymentCodeInfoDTO;
		if (paymentCodeInfoDTO) {
			// 设置会员信息
			this.getLoginInfo();
		} else {
			app.globalData.eventBus.on('qryInfo', () => {
				// 设置会员信息
				this.getLoginInfo();
			});
		}
	},
	/**
	 * 生命周期函数--监听页面隐藏
	 */
	onHide: function() {},
	/**
	 * 生命周期函数--监听页面卸载
	 */
	onUnload: function() {},
	/**
	 * 页面相关事件处理函数--监听用户下拉动作
	 */
	onPullDownRefresh: function() {},
	/**
	 * 页面上拉触底事件的处理函数
	 */
	onReachBottom: function() {},
	/**
	 * 用户点击右上角分享
	 */
	onShareAppMessage: function() {},
	methods: {
		getLoginInfo() {
			const { memberId, paymentCodeInfoDTO} = app.globalData.userInfo;
			
			// 设置商户信息
			this.setMchInfo(paymentCodeInfoDTO);
			
			if(memberId) {
				this.getInfo();
			} else {
				if(global.fromGzh) {
					const { mchNo, filePath } = paymentCodeInfoDTO;
					this.showPhoneDialog = true;
					this.mchNo = mchNo;
					this.filePath = filePath;
				}
			}
		},

		setMchInfo(paymentCodeInfoDTO) {
			const { openLightWeightMemberStandard, openFLowOilCardSave, openMemberMarketingProduct } = paymentCodeInfoDTO;
			this.openLightWeightMemberStandard = openLightWeightMemberStandard;
			this.openFLowOilCardSave = openFLowOilCardSave;
			this.openMemberMarketingProduct = openMemberMarketingProduct;

			if (app.globalData.userInfo.oilCardAccount) {
				this.oilBalance = app.globalData.userInfo.oilCardAccount.balance;
			}
		},

		closePhoneDialogSp: function() {
			this.showPhoneDialog = false;
		},

		getPhoneNumber: function(e) {
			const memberId = app.globalData.userInfo.memberId;
			if (memberId) {
				this.showPhoneDialog = false;

				this.getInfo();
			}
		},

		getInfo: function(params = {}) {
			const memberId = app.globalData.userInfo.memberId;
			params.memberId = memberId;

			getInfo(params).then(res => {
				if (res.code === 200) {
					return res.data;
				} else {
					uni.showToast({
						title: res.msg,
						icon: 'error',
						duration: 2000
					});

					throw new Error(res.msg);
				}
			}).then(data => {
				const { headImgUrl, wxNickName, phoneNo, points, pointsDetails, useNum } = data;

				this.memberId = memberId;
				this.wxNickName = wxNickName;
				this.headImgUrl = headImgUrl;
				this.phoneNo = phoneNo;
				this.points = points;
				this.pointsDetails = pointsDetails;
				this.useNum = useNum;
			});
		},

		jumpCommon: function(value, allow) {
			if (allow) {
				if (value.indexOf('checkstand') !== -1) {
					uni.reLaunch({
						url: value
					});
				} else {
					if(this.memberId || value.indexOf('oilCard') !== -1) {
						uni.navigateTo({
							url: value
						});
					} else {
						this.showPhoneDialog = true;
					}
				}
			} else {
				uni.showToast({
					title: '敬请期待~~',
					icon: 'none'
				});
			}
		}
	}
};
</script>
<style>
@import './member.css';
</style>

<style lang="scss" scoped>
/deep/ .u-modal__content {
	padding: 0 !important;
}

/deep/ .u-modal__button-group--confirm-button {
	padding: 0 !important;
}
</style>
