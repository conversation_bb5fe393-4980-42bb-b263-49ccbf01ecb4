<template>
	<view class="refueling-detail">
		<view class="refueling-detail-header flex-column flex-center">
			<text :class="status === 100 ? 'color-success' : 'color-error'">{{status | orderStatusFt}}</text>
			<text class="bold" style="padding-top: 5px;">￥{{ amount | moneyFt }}</text>
		</view>
		<view class="common-item refueling-order-item flex-column">
			<text class="common-title">订单信息</text>
			<view class="common-item-item flex-jc-between">
				<text class="color-grey">交易时间</text>
				<text>{{ createTime | timeFt }}</text>
			</view>
			<view class="common-item-item flex-jc-between">
				<text class="color-grey">订单编号</text>
				<text>{{ orderNo }}</text>
			</view>
			<view class="common-item-item flex-jc-between">
				<text class="color-grey">付款方式</text>
				<text>{{ payWayDesc }}</text>
			</view>
			<view class="common-item-item flex-jc-between">
				<text class="color-grey">加油金额</text>
				<text>￥{{ originalAmount | moneyFt }}</text>
			</view>
			<view class="common-item-item flex-jc-between">
				<text class="color-grey">优惠金额</text>
				<text>￥{{ discountAmount | moneyFt }}</text>
			</view>
			<view class="common-item-item flex-jc-between">
				<text class="color-grey">服务费</text>
				<text>￥{{ platServiceFee | moneyFt }}</text>
			</view>
		</view>
		<view class="common-item refueling-product-item flex-column">
			<text class="common-title">商品信息</text>
			<view class="common-item-item flex-jc-between">
				<text class="color-grey">加油信息</text>
				<text>{{ orderDesc }}</text>
			</view>
			<view class="common-item-item flex-jc-between">
				<text class="color-grey">加油单价</text>
				<text>￥{{ oilSingleFinalPrice | moneyFt }}/L</text>
			</view>
			<view class="common-item-item flex-jc-between">
				<text class="color-grey">加油升数</text>
				<text>{{ oilLiters | moneyFt }}/L</text>
			</view>
		</view>
	</view>
</template>

<script>
import { getOrderInfo } from '../../../api/2-refueling';

export default {
	data() {
		return {
			amount: 0,
			orderNo: '',
			status: 0,
			createTime: '',
			discountAmount: 0,
			platServiceFee: 0,
			isOpenService: 0,
			originalAmount: 0,
			payWayDesc: '',
			oilLiters: 0,
			oilSingleFinalPrice: 0,
			orderDesc: ''
		};
	},
	/**
	 * 生命周期函数--监听页面加载
	 */
	onLoad: function(options) {
		const { orderNo, memberId, mchNo } = options;

		let params = {
			orderNo,
			memberId,
			mchNo
		};
		this.getOrderInfo(params);
	},
	/**
	 * 生命周期函数--监听页面初次渲染完成
	 */
	onReady: function() {},
	/**
	 * 生命周期函数--监听页面显示
	 */
	onShow: function() {},
	/**
	 * 生命周期函数--监听页面隐藏
	 */
	onHide: function() {},
	/**
	 * 生命周期函数--监听页面卸载
	 */
	onUnload: function() {},
	/**
	 * 页面相关事件处理函数--监听用户下拉动作
	 */
	onPullDownRefresh: function() {},
	/**
	 * 页面上拉触底事件的处理函数
	 */
	onReachBottom: function() {},
	/**
	 * 用户点击右上角分享
	 */
	onShareAppMessage: function() {},
	methods: {
		getOrderInfo(params) {
			getOrderInfo(params).then(res => {
				if (res.code === 200) {
					return res.data;
				} else {
					uni.showToast({
						title: res.msg,
						icon: 'loading',
						duration: 2000
					});
					
					throw new Error(res.msg);
				}
			}).then(data => {
				const {amount,orderNo,status,createTime,discountAmount,platServiceFee,isOpenService,originalAmount,
						payWayDesc,oilLiters,oilSingleFinalPrice,orderDesc} = data;

				this.amount = amount;
				this.orderNo = orderNo;
				this.status = status;
				this.createTime = createTime;
				this.discountAmount = discountAmount;
				this.platServiceFee = platServiceFee;
				this.isOpenService = isOpenService;
				this.originalAmount = originalAmount;
				this.payWayDesc = payWayDesc;
				this.oilLiters = oilLiters;
				this.oilSingleFinalPrice = oilSingleFinalPrice;
				this.orderDesc = orderDesc;
			});
		}
	}
};
</script>

<style scoped>
@import './refuelingDetail.css';
</style>
