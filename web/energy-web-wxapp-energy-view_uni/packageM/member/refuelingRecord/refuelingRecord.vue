<template>
	<view class="refueling-record">
		<view class="header flex-jc-between">
			<text>全部交易</text>
			<text>成功支付{{statisticalData.totalSuccessOrderAmount | moneyFt}}元，完成加油{{statisticalData.totalSuccessCount}}次</text>
		</view>
		<template v-for="(item, index) in recordList">
			<view class="flex-jc-between refueling-record-item fz-26" @click="jumpToRefuelingDetail(item.orderNo)" :key="index">
				<view class="flex-column">
					<text>订单号: {{ item.orderNo }}</text>
					<text>优惠总额: ￥{{ item.discountAmount | moneyFt }}</text>
					<text>创建时间: {{ item.createTime | timeFt }}</text>
				</view>
				<view class="flex-column flex-end">
					<text class="fz-32 bold">￥{{ item.amount }}</text>
					<text :class="item.status === 100 ? 'color-success' : 'color-error'">{{item.status | orderStatusFt}}</text>
				</view>
			</view>
		</template>
		<view class="load-content flex-center">
			<u-loadmore fontSize="30" color="#999" :status="status" :loading-text="loadingText" :loadmore-text="loadmoreText" :nomore-text="nomoreText" />
		</view>
	</view>
</template>

<script>
import {qryPayOrder, statisticsPayOrder} from '../../api/2-refueling'		
	
const app = getApp();	
export default {
	data() {
		return {
			status: 'loadmore', // loadmore/loading/nomore
			loadingText: '加载中...',
			loadmoreText: '上拉加载数据',
			nomoreText: '到底了',
			pageNum: 1,
			statisticalData: {
				totalSuccessCount: 0,
				totalSuccessOrderAmount: 0
			},
			recordList: []
		};
	},
	/**
	 * 生命周期函数--监听页面加载
	 */
	onLoad: function(options) {
	},
	/**
	 * 生命周期函数--监听页面初次渲染完成
	 */
	onReady: function() {},
	/**
	 * 生命周期函数--监听页面显示
	 */
	onShow: function() {
		this.getRefuelingRecord();
	},
	/**
	 * 生命周期函数--监听页面隐藏
	 */
	onHide: function() {},
	/**
	 * 生命周期函数--监听页面卸载
	 */
	onUnload: function() {},
	/**
	 * 页面相关事件处理函数--监听用户下拉动作
	 */
	onPullDownRefresh: function() {
		console.log('下拉刷新中');

		this.getRefuelingRecord(false);
	},
	/**
	 * 页面上拉触底事件的处理函数
	 */
	onReachBottom: function() {
		console.log('触底');
		
		if(this.status!=='nomore') {
			this.getRefuelingRecord(true);
		}
	},
	/**
	 * 用户点击右上角分享
	 */
	onShareAppMessage: function() {},
	methods: {
		jumpToRefuelingDetail(orderNo) {
			const memberId = app.globalData.userInfo.memberId;
			const mchNo = app.globalData.userInfo.paymentCodeInfoDTO.mchNo;
			
			uni.navigateTo({
				url: '/packageM/member/refuelingRecord/refuelingDetail/refuelingDetail?orderNo='+orderNo+'&memberId='+memberId+'&mchNo='+mchNo
			})
		},
		getRefuelingRecord(addPage = false) {
			this.status = 'loading';
			
			const memberId = app.globalData.userInfo.memberId;
			const mchNo = app.globalData.userInfo.paymentCodeInfoDTO.mchNo;
			
			const searchParams = {
				memberId: memberId,
				mchNo: mchNo,
				startTime: '',
				endTime: '',
				pageNum: addPage ? this.pageNum + 1 : 1,
				numPerPage: 10
			}
			
			Promise.all([qryPayOrder(searchParams), statisticsPayOrder(searchParams)]).then(res => {
				// 停止下拉刷新
				uni.stopPullDownRefresh();
			    if (res[0].code !== 200) {
					uni.showToast({
						title: res[0].msg,
						icon: 'error',
						duration: 2000
					});
			        throw new Error(res[0].msg);
			    }
				
			    if (res[1].code !== 200) {
					uni.showToast({
						title: res[1].msg,
						icon: 'error',
						duration: 2000
					});
			        throw new Error(res[1].msg);
			    }
			    return [res[0].data, res[1].data];
			}).then(([listData, sumData]) => {
				if(listData){
					const currList = listData.data ? listData.data : [];
					
					if (addPage) {
						if (this.recordList.length < listData.totalRecord) {
							for (let i = this.recordList.length - 1; i >= 0; i--) {
								currList.unshift(this.recordList[i]);
							}
							this.recordList = currList;
							this.status = 'loadmore'
					    } else {
					        this.status = 'nomore'
					    }
					} else {
						this.recordList = currList;
						this.status = currList.length === listData.totalRecord ? 'nomore' : 'loadmore' 
					}
					
					this.pageNum = listData.pageCurrent;
				}
				
				if(sumData) {
					this.statisticalData.totalSuccessCount = sumData.totalSuccessCount;
					this.statisticalData.totalSuccessOrderAmount = sumData.totalSuccessOrderAmount;
				}
			});
		}
	}
};
</script>

<style scoped>
@import './refuelingRecord.css';
</style>

<style lang="scss" scoped>
/deep/ .u-loading-icon__spinner {
	width: 15px !important;
	height: 15px !important;
}
</style>
