page {
    background-color: #fafafa;
}

.fz-36 {
	font-size: 36rpx;
}

.fz-30 {
	font-size: 30rpx;
}

.fz-26 {
	font-size: 26rpx;
}

.bold {
	font-weight: 800;
}

.color-red {
	color: #fd0000;
}

.common-box {
	width: 84vw;
	padding: 1vh 3.5vw;
	border: 1.6px solid #ccc;
	border-radius: 5px;
	margin-top: 3vh;
}

.recharge-amount-item {
	width: 20vw;
	border-radius: 5px;
	padding: 0.5vh 3vw;
	margin: 1vh calc((6vw - 9.6px)/2) 1vh 0;
}

.select-active {
    background-color: rgba(252, 193, 0, 0.1);
    color: #ff9900;
    border: 1.6px solid #ff9900;
}

.select-noactive {
    color: #333;
    border: 1.6px solid #ccc;
}

.recharge-amount-item:nth-child(3n) {
	margin: 1vh 0;
}

.pay-way {
	padding-top: 2vh;
	padding-bottom: 1vh;
}

.btn-box {
	position: fixed;
	bottom: calc(3vh + env(safe-area-inset-bottom));
}