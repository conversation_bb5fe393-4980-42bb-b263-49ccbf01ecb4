<template>
	<view class="flex-column flex-center">
		<view class="common-box">
			<text class="fz-36 bold">选择充值金额</text>
			<view class="flex-wrap">
				<template v-if="oilVariety === '2'">
					<view v-for="(item, index) in group['2']" :class="'recharge-amount-item flex-column flex-center ' + (activeAmount === item.rechargeAmount ? 'select-active' : 'select-noactive') " :key="index" @click="selectAmount(item)">
						<text class="fz-30 bold">￥{{item.rechargeAmount}}</text>
						<text class="fz-26">满送{{item.marketingAmount}}</text>
					</view>
				</template>
				<template v-else>
					<view v-for="(item, index) in group['1']" :class="'recharge-amount-item flex-column flex-center ' + (activeAmount === item.rechargeAmount ? 'select-active' : 'select-noactive') " :key="index" @click="selectAmount(item)">
						<text class="fz-30 bold">￥{{item.rechargeAmount}}</text>
						<text class="fz-26">满送{{item.marketingAmount}}</text>
					</view>
				</template>
			</view>
		</view>
		<view class="common-box">
			<text class="fz-36 bold">选择支付方式</text>
			<view class="flex-jc-between pay-way">
				<template v-if="provider==='weixin'">
					<u-radio-group placement="column" iconPlacement="right" size="40" iconSize="30">
						<view class="flex-jc-between">
							<u-icon label="微信支付" size="40" imgMode="widthFix" name="/static/image/public/wxpay.png"></u-icon>
							<u-radio activeColor="red"></u-radio>
						</view>
					</u-radio-group>
				</template>
				<template v-else>
					<u-radio-group placement="column" iconPlacement="right" size="40" iconSize="30">
						<view class="flex-jc-between">
							<u-icon label="支付宝支付" size="40" imgMode="widthFix" name="/static/image/public/alipay.png"></u-icon>
							<u-radio activeColor="red"></u-radio>
						</view>
					</u-radio-group>
				</template>
			</view>
		</view>
		<view class="btn-box flex-column flex-center">
			<text class="fz-30" v-if="activeAmount>0">电子卡加油充值<text class="color-red">{{activeAmount}}</text>元<template v-if="activeMarketingAmount>0">，赠送油额<text class="color-red">{{activeMarketingAmount}}</text>元</template></text>
			<u-button :disabled="disabled" :loading="loading" loadingSize="30" loadingText="支付中" :customStyle="btnCustomStyle" @click="recharge">{{activeAmount>0 ? '￥'+activeAmount : ''}} 充值支付</u-button>
		</view>
	</view>
</template>

<script>
import { qryFlowOilCardAccount } from '../../../../api/0-common';
import { qryRechargeConfig } from '../../../api/1-oilCard';
import { payType, payStatus, commontPayment } from '../../../../utils/commonPay';

let app = getApp();
export default {
	data() {
		return {
			btnCustomStyle: {
				width: '80vw',
				height: '6vh',
				borderRadius: '5px',
				border: 'none',
				background: '#ff9900',
				color: '#fff',
				marginTop: '1vh'
			},
			disabled: true,
			loading: false,
			provider: '',
			mchNo: '',
			marketingType: '', //1=满送 2=满减
			oilVariety: '', //0:不区分 1: 汽油 2: 柴油
			group: null,
			activeAmount: 0, //被选中的金额
			activeMarketingAmount: 0, //被选中的营销金额
			activeOilVariety: 0 //被选中油品种类 1: 汽油 2: 柴油
		}
	},
	/**
	 * 生命周期函数--监听页面加载
	 */
	onLoad: function(options) {
		const {oilVariety} = options;
		let title = oilVariety === '0' ? '电子卡充值' : oilVariety === '1' ? '电子卡充值-汽油' : '电子卡充值-柴油';
		uni.setNavigationBarTitle({
			title: title
		})
		
		const paymentCodeInfoDTO = app.globalData.userInfo.paymentCodeInfoDTO;
		this.provider = app.globalData.provider;
		this.oilVariety = oilVariety;
		this.mchNo = paymentCodeInfoDTO.mchNo;
		this.payWay = app.globalData.provider === 'weixin' ? 'WEIXIN' : 'ALIPAY';
	},
	/**
	 * 生命周期函数--监听页面初次渲染完成
	 */
	onReady: function() {
	},
	/**
	 * 生命周期函数--监听页面显示
	 */
	onShow: function() {
		this.getRechargeConfig();
	},
	/**
	 * 生命周期函数--监听页面隐藏
	 */
	onHide: function() {
	},
	/**
	 * 生命周期函数--监听页面卸载
	 */
	onUnload: function() {},
	/**
	 * 页面相关事件处理函数--监听用户下拉动作
	 */
	onPullDownRefresh: function() {},
	/**
	 * 页面上拉触底事件的处理函数
	 */
	onReachBottom: function() {},
	/**
	 * 用户点击右上角分享
	 */
	onShareAppMessage: function() {},
	methods: {
		getRechargeConfig() {
			const params = {
				mchNo: this.mchNo
			}
			
			qryRechargeConfig(params).then(res=> {
				if(res.code===200) {
					return res.data;
				} else {
					uni.showToast({
					    title: res.msg,
					    icon: 'loading',
					    duration: 2000
					});
					
					throw new Error(res.msg);
				}
			}).then(({ marketingType, group })=> {
				this.marketingType = marketingType;
				this.group = group;
			})
		},
		selectAmount(e) {
			this.activeAmount = e.rechargeAmount;
			this.activeMarketingAmount = e.marketingAmount;
			this.activeOilVariety = e.type;
			this.disabled = false;
		},
		recharge() {
			this.loading = true;
			uni.showLoading({
				title: '支付中',
				mask: true
			});
			
			const pcn = app.globalData.pcn;
			const params = {
				type: payType.oilPay,
				mchNo: this.mchNo,
				memberId: app.globalData.userInfo.memberId,
				payWay: this.payWay,
				paymentCodeNo: pcn,
				balanceType: this.oilVariety === '2' ?  2 : 1,
				orderAmount: this.activeAmount
			}
			
			const oilVariety = this.activeOilVariety;
			commontPayment(params).then(res=> {
				if(res.code === payStatus.success) {
					if (res.msg === 'requestPayment:ok') {
						// #ifdef MP-WEIXIN
						uni.showToast({
							title: '支付成功',
							icon: 'success',
							mask: true,
							duration: 2000,
							success: ()=> {
								this.updateAccount();
												
								this.jumpToResult('success', params.orderAmount, oilVariety, '');
							}
						});
						// #endif
						// #ifdef MP-ALIPAY
						if(res.resultCode === '9000') {
							uni.showToast({
								title: '支付成功',
								icon: 'success',
								mask: true,
								duration: 2000,
								success: ()=> {
									this.updateAccount();
					
									this.jumpToResult('success', params.orderAmount, oilVariety, '');
								}
							});
						} else {
							uni.showToast({
								title: '您未完成支付',
								icon: 'error',
								mask: true,
								duration: 3000,
							});
						}
						// #endif
					} else {
						uni.showToast({
							title: '支付完成！',
							icon: 'success',
							mask: true,
							duration: 3000,
							success: ()=> {
								this.updateAccount();
												
								this.jumpToResult('success', params.orderAmount, oilVariety, '');
							}
						});
					}
				}
			}).catch((res)=> {
				if(res) {
					if(res.code === payStatus.cancel) {
						if (res.msg === 'requestPayment:fail cancel') {
							uni.showToast({
								title: '您已取消支付',
								icon: 'error',
								mask: true,
								duration: 3000
							});
						} else {
							uni.showToast({
								title: res.msg,
								icon: 'error',
								mask: true,
								duration: 3000,
								success: ()=> {
									this.jumpToResult('error', params.orderAmount, oilVariety, res.msg);
								}
							});
						}
					} else if (res.code === payStatus.fail) {
						this.jumpToResult('error', params.orderAmount, oilVariety, res.data.errMsg);
					}
				}
			}).finally(()=> {
				// #ifdef MP-WEIXIN
				wx.hideLoading({
					noConflict: true
				});
				// #endif
				
				// #ifdef MP-ALIPAY
				my.hideLoading();
				// #endif
				
				this.loading = false;
				// 初始化
				this.activeAmount = 0;
				this.activeMarketingAmount = 0;
				this.activeOilVariety = 0;
				this.disabled = true;
			})
		},
		jumpToResult(status, orderAmount, oilVariety, msg) {
			uni.navigateTo({
				url: '/packageM/member/oilCard/rechargeResult/rechargeResult?status='+ status + "&orderAmount=" + orderAmount + "&oilVariety=" + oilVariety + "&errMsg=" + msg
			})
		},
		updateAccount() {
			// 获取用户油卡账户信息
			qryFlowOilCardAccount({memberId: app.globalData.userInfo.memberId}).then(res=> {
				if (res.code === 200) {
					if(res.data) {
						app.globalData.userInfo.oilCardAccount = res.data;
					}
				}
			})
		}
	}
}
</script>

<style scoped>
@import './recharge.css';
</style>

<style lang="scss" scoped>
/deep/ .u-button::before {
	opacity: 0 !important;
}
</style>