<template>
	<view class="transaction-container">
		<view class="flex-column flex-center">
			<view class="transaction-header flex-center">
				<u-icon size="180" imgMode="widthFix" name="/static/image/public/success.png"></u-icon>
				<view class="transaction-header-item flex-column">
					<text class="success fz-45 bold">您已消费成功！</text>
					<view class="transaction-header-item-item fz-30">
						<text class="title">商家：</text>
						<text style="word-break: break-all;">{{mchName}}</text>
					</view>
					<view class="transaction-header-item-item fz-30">
						<text class="title">金额：</text>
						<text class="color-red bold">{{orderFinalAmount}}</text>
					</view>
					<view class="transaction-header-item-item fz-30">
						<text class="title">油卡余额：</text>
						<text class="color-red bold">{{balance}}</text>
					</view>
				</view>
			</view>
			<view>
				<u-button type="primary" color="#ff9900" :customStyle="btnCustomStyle" @click="jumpToTransactionRecord">查看订单详情</u-button>
				<navigator target="miniProgram" open-type='exit'>
					<u-button :customStyle="btnBiggerCustomStyle">关闭</u-button>
				</navigator>
			</view>
		</view>
	</view>
</template>

<script>
import {qryFlowOilCardAccount} from '../../../../api/0-common'	
	
const app = getApp();	
export default {
	data() {
		return {
			btnCustomStyle: {
				width: '75vw',
				height: '7vh',
				borderRadius: '3.5vh',
				marginTop: '3vh'
			},
			btnBiggerCustomStyle: {
				width: '75vw',
				height: '8vh',
				borderRadius: '4vh',
				marginTop: '3vh'
			},
			oilVariety: '',
			mchName: '',
			orderFinalAmount: 0,
			balance: 0
		}
	},
	/**
	 * 生命周期函数--监听页面加载
	 */
	onLoad: function(options) {
		const {oilVariety,orderFinalAmount,mchName} = options;
		this.oilVariety = oilVariety;
		this.orderFinalAmount = orderFinalAmount;
		this.mchName = mchName;
		
		this.getAccount();
	},
	/**
	 * 生命周期函数--监听页面初次渲染完成
	 */
	onReady: function() {
	},
	/**
	 * 生命周期函数--监听页面显示
	 */
	onShow: function() {
	},
	/**
	 * 生命周期函数--监听页面隐藏
	 */
	onHide: function() {
	},
	/**
	 * 生命周期函数--监听页面卸载
	 */
	onUnload: function() {},
	/**
	 * 页面相关事件处理函数--监听用户下拉动作
	 */
	onPullDownRefresh: function() {},
	/**
	 * 页面上拉触底事件的处理函数
	 */
	onReachBottom: function() {},
	/**
	 * 用户点击右上角分享
	 */
	onShareAppMessage: function() {},
	methods: {
		jumpToTransactionRecord() {
			uni.navigateTo({
				url: '/packageM/member/oilCard/transactionRecord/transactionRecord'
			});
		},
		getAccount() {
			const params = {
				memberId: app.globalData.userInfo.memberId
			}
			qryFlowOilCardAccount(params).then(res=> {
				if(res.code === 200) {
					app.globalData.userInfo.oilCardAccount = res.data;
					
					return res.data;
				} else {
					uni.showToast({
					    title: res.msg,
					    icon: 'loading',
					    duration: 2000
					});
					
					throw new Error(res.msg);
				}
			}).then(({ balance, petrolBalance, dieselBalance })=> {
				if(app.globalData.userInfo.paymentCodeInfoDTO.rechargeConfig.configType === 100) {
					if(this.oilVariety === '1') {
						this.balance = petrolBalance;
					} else {
						this.balance = dieselBalance;
					}
				} else {
					this.balance = balance;
				}
			})
		},
	}
}
</script>

<style scoped>
@import './transactionResult.css';
</style>