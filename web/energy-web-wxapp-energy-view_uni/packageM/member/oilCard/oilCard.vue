<template>
	<view class="oil-card">
		<template v-if="openOilCard">
			<view class="common-content">
				<template v-if="configType === 100">
					<view class="common-item flex-center" >
						<image class="common-item-img" src="../../static/member/petrol.png"></image>
						<text class="common-item-account fz-24 color-sp">NO.{{ accountNo }}</text>
						<text class="common-item-phone fz-24 color-sp">{{ memberPhone }}</text>
						<text class="common-item-amount fz-36 bold color-w">余额：{{ petrolBalance }} 元</text>
						<view class="common-item-button flex">
							<view style="width: 45vw; height: 7vh;" @click="jumpToRecharge(1)"></view>
							<view style="width: 45vw; height: 7vh;" @click="jumpToTransactionRecord"></view>
						</view>
					</view>
					<view class="common-item flex-center" style="margin-top: 2vh;">
						<image class="common-item-img" src="../../static/member/diesel.png"></image>
						<text class="common-item-account fz-24 color-sp">NO.{{ accountNo }}</text>
						<text class="common-item-phone fz-24 color-sp">{{ memberPhone }}</text>
						<text class="common-item-amount fz-36 bold color-w">余额：{{ dieselBalance }} 元</text>
						<view class="common-item-button flex">
							<view style="width: 45vw; height: 7vh;" @click="jumpToRecharge(2)"></view>
							<view style="width: 45vw; height: 7vh;" @click="jumpToTransactionRecord"></view>
						</view>
					</view>
				</template>
				<template v-else>
					<view class="common-item flex-center">
						<image class="common-item-img" src="../../static/member/common.png"></image>
						<text class="common-item-account fz-24 color-sp">NO.{{ accountNo }}</text>
						<text class="common-item-phone fz-24 color-sp">{{ memberPhone }}</text>
						<text class="fz-36 bold color-w">余额：{{ balance }} 元</text>
						<view class="common-item-button flex">
							<view style="width: 45vw; height: 7vh;" @click="jumpToRecharge(0)"></view>
							<view style="width: 45vw; height: 7vh;" @click="jumpToTransactionRecord"></view>
						</view>
					</view>
				</template>
			</view>
		</template>
		<template v-else>
			<view class="common-content">
				<image class="nonactivated-img" mode="widthFix" src="../../static/member/no-open.png"></image>
				<view class="flex-center" style="margin-top: 15vh;">
					<u-button
						:customStyle="btnCustomStyle"
						type="primary"
						shape="circle"
						color="linear-gradient(to bottom, #F5D698, #E4C385)"
						text="开通加油卡"
						@click="openCard"
					></u-button>
				</view>
			</view>
		</template>

		<view v-if="showPhoneDialog">
			<my-authorization :mchNo="mchNo" :authStep="1" :filePath="filePath" @closePhoneDialogSp="closePhoneDialogSp" @getPhoneNumber="getPhoneNumber"></my-authorization>
		</view>
	</view>
</template>

<script>
import { qryFlowOilCardAccount } from '../../../api/0-common';
import { openOilCardAccount } from '../../api/1-oilCard';
import myAuthorization from '../../../components/authorization/authorization';

const app = getApp();
export default {
	components: {
		'my-authorization': myAuthorization
	},
	data() {
		return {
			btnCustomStyle: {
				width: '60vw',
				height: '6vh'
			},
			openOilCard: false, //是否开通加油卡
			isExisted: false, //会员是否存在
			showPhoneDialog: false,
			filePath: '',
			mchNo: '',
			accountNo: '',
			balance: 0,
			petrolBalance: 0,
			dieselBalance: 0,
			memberPhone: '',
			configType: 0 //是否区分汽柴油 100=区分 101=不区分
		};
	},
	/**
	 * 生命周期函数--监听页面加载
	 */
	onLoad: function(options) {
		const memberId = app.globalData.userInfo.memberId;
		const paymentCodeInfoDTO = app.globalData.userInfo.paymentCodeInfoDTO;
		if (memberId) {
			this.memberId = memberId;
			this.isExisted = true;
		}

		this.mchNo = paymentCodeInfoDTO.mchNo;
		this.filePath = paymentCodeInfoDTO.filePath;
		this.configType = paymentCodeInfoDTO.rechargeConfig.configType;
	},
	/**
	 * 生命周期函数--监听页面初次渲染完成
	 */
	onReady: function() {},
	/**
	 * 生命周期函数--监听页面显示
	 */
	onShow: function() {
		if(this.isExisted) {
			this.getAccount();
		}
	},
	/**
	 * 生命周期函数--监听页面隐藏
	 */
	onHide: function() {},
	/**
	 * 生命周期函数--监听页面卸载
	 */
	onUnload: function() {},
	/**
	 * 页面相关事件处理函数--监听用户下拉动作
	 */
	onPullDownRefresh: function() {},
	/**
	 * 页面上拉触底事件的处理函数
	 */
	onReachBottom: function() {},
	/**
	 * 用户点击右上角分享
	 */
	onShareAppMessage: function() {},
	methods: {
		getAccount: function() {
			const params = {
				memberId: app.globalData.userInfo.memberId
			};
			
			qryFlowOilCardAccount(params).then(res => {
				if (res.code === 200) {
					return res.data;
				} else {
					uni.showToast({
						title: res.msg,
						icon: 'loading',
						duration: 2000
					});
					
					throw new Error(res.msg);
				}
			}).then((data) => {
				if(data) {
					const { accountNo, balance, petrolBalance, dieselBalance, memberPhone } = data;
					
					this.openOilCard = true;
					this.accountNo = accountNo;
					this.balance = balance;
					this.petrolBalance = petrolBalance;
					this.dieselBalance = dieselBalance;
					this.memberPhone = memberPhone;
						
					app.globalData.userInfo.oilCardAccount = data;
				}
			});
		},
		openCard: function() {
			if (!this.isExisted) {
				// 走注册流程
				this.showPhoneDialog = true;
			} else {
				const params = {
					memberId: app.globalData.userInfo.memberId,
					mchNo: this.mchNo
				};
				// 单纯开卡
				openOilCardAccount(params).then(res => {
					if (res.code === 200) {
						this.getAccount();
					} else {
						uni.showToast({
							title: res.msg,
							icon: 'loading',
							duration: 2000
						});
					}
				});
			}
		},
		closePhoneDialogSp: function() {
			this.showPhoneDialog = false;
		},
		getPhoneNumber: function(e) {
			const { isExisted } = e.detail;
			this.isExisted = isExisted;
			this.showPhoneDialog = false;

			if (isExisted) {
				this.openCard();
			}
		},
		jumpToRecharge: function(oilVariety) {
			uni.navigateTo({
				url: '/packageM/member/oilCard/recharge/recharge?oilVariety=' + oilVariety
			});
		},
		jumpToTransactionRecord: function() {
			uni.navigateTo({
				url: '/packageM/member/oilCard/transactionRecord/transactionRecord'
			});
		}
	}
};
</script>

<style scoped>
@import './oilCard.css';
</style>

<style lang="scss" scoped>
/deep/ .u-modal__content {
	padding: 0 !important;
}

/deep/ .u-modal__button-group--confirm-button {
	padding: 0 !important;
}
</style>
