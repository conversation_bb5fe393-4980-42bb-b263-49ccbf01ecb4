<template>
	<view class="transaction-record">
		<view class="header flex-jc-between">
			<text class="bold">全部交易</text>
			<text>充值<text class="bold">{{statisticalData.rechargeTotalAmount}}元</text>，消费<text class="bold">{{statisticalData.consumeTotalAmount}}元</text></text>
		</view>
		<template v-for="(item, index) in recordList">
			<view class="flex-center flex-jc-between transaction-record-item" @click="jumpToTransactionDetail(item.orderType, item.orderNo)" :key="index">
				<view class="flex-column">
					<text>{{ item.title }}</text>
					<text class="complete-time">{{ item.createTime | timeFt }}</text>
				</view>
				<text class="bold">{{ item.alterAmountStr }}</text>
			</view>
		</template>
		<view class="load-content flex-center">
			<u-loadmore fontSize="30" color="#999" :status="status" :loading-text="loadingText" :loadmore-text="loadmoreText" :nomore-text="nomoreText" />
		</view>
	</view>
</template>

<script>
import {queryOrder, getTransactions} from '../../../api/1-oilCard'		
	
let app = getApp();	
export default {
	data() {
		return {
			status: 'loadmore', // loadmore/loading/nomore
			loadingText: '加载中...',
			loadmoreText: '上拉加载数据',
			nomoreText: '到底了',
			pageNum: 1,
			statisticalData: {
				rechargeTotalAmount: 0,
				consumeTotalAmount: 0
			},
			recordList: []
		};
	},
	/**
	 * 生命周期函数--监听页面加载
	 */
	onLoad: function(options) {
	},
	/**
	 * 生命周期函数--监听页面初次渲染完成
	 */
	onReady: function() {},
	/**
	 * 生命周期函数--监听页面显示
	 */
	onShow: function() {
		this.getTransactions();
	},
	/**
	 * 生命周期函数--监听页面隐藏
	 */
	onHide: function() {},
	/**
	 * 生命周期函数--监听页面卸载
	 */
	onUnload: function() {},
	/**
	 * 页面相关事件处理函数--监听用户下拉动作
	 */
	onPullDownRefresh: function() {
		console.log('下拉刷新中');

		this.getTransactions(false);
	},
	/**
	 * 页面上拉触底事件的处理函数
	 */
	onReachBottom: function() {
		console.log('触底');
		
		if(this.status!=='nomore') {
			this.getTransactions(true);
		}
	},
	/**
	 * 用户点击右上角分享
	 */
	onShareAppMessage: function() {},
	methods: {
		jumpToTransactionDetail(orderType, orderNo) {
			const accountNo = app.globalData.userInfo.oilCardAccount.accountNo;
			
			uni.navigateTo({
				url: '/packageM/member/oilCard/transactionDetail/transactionDetail?accountNo=' + accountNo + '&orderType=' + orderType + '&orderNo=' + orderNo
			});
		},
		getTransactions(addPage = false) {
			this.status = 'loading';
			
			const memberId = app.globalData.userInfo.memberId;
			const accountNo = app.globalData.userInfo.oilCardAccount.accountNo;
			
			const params = {
				memberId: memberId,
			}
			
			const searchParams = {
				accountNo: accountNo,
				pageNum: addPage ? this.pageNum + 1 : 1,
				numPerPage: 10
			}
			
			Promise.all([queryOrder(searchParams), getTransactions(params)]).then(res => {
				// 停止下拉刷新
				uni.stopPullDownRefresh();
			    if (res[0].code !== 200) {
					uni.showToast({
						title: res[0].msg,
						icon: 'error',
						duration: 2000
					});
			        throw new Error(res[0].msg);
			    }
				
			    if (res[1].code !== 200) {
					uni.showToast({
						title: res[1].msg,
						icon: 'error',
						duration: 2000
					});
			        throw new Error(res[1].msg);
			    }
			    return [res[0].data, res[1].data];
			}).then(([listData, sumData]) => {
				if(listData){
					const currList = listData.data ? listData.data : [];
					
					if (addPage) {
						if (this.recordList.length < listData.totalRecord) {
							for (let i = this.recordList.length - 1; i >= 0; i--) {
								currList.unshift(this.recordList[i]);
							}
							this.recordList = currList;
							this.status = 'loadmore'
					    } else {
					        this.status = 'nomore'
					    }
					} else {
						this.recordList = currList;
						this.status = currList.length === listData.totalRecord ? 'nomore' : 'loadmore' 
					}
					
					this.pageNum = listData.pageCurrent;
				}
				
				if(sumData) {
					this.statisticalData.rechargeTotalAmount = sumData.rechargeTotalAmount;
					this.statisticalData.consumeTotalAmount = sumData.consumeTotalAmount;
				}
			});
		}
	}
};
</script>

<style scoped>
@import './transactionRecord.css';
</style>

<style lang="scss" scoped>
/deep/ .u-loading-icon__spinner {
	width: 15px !important;
	height: 15px !important;
}
</style>
