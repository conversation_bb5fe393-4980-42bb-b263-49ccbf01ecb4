<template>
	<view class="recharge-result flex-column flex-center">
		<template v-if="result==='success'">
			<u-icon size="80" imgMode="widthFix" name="/static/image/public/success.png"></u-icon>
			<text class="fz-30">充值成功：{{orderAmount}}元</text>
			<u-button :customStyle="btnCustomStyle" @click="jumpToCheckstand">去加油</u-button>
		</template>
		<template v-else>
			<u-icon size="80" imgMode="widthFix" name="/static/image/public/fail.png"></u-icon>
			<text class="fz-40 bold title">充值失败</text>
			<text class="fz-30 reason">{{errMsg}}</text>
			<u-button :customStyle="btnCustomStyle" @click="jumpToRecharge">返回</u-button>
		</template>
	</view>
</template>

<script>
let app = getApp();	
export default {
	data() {
		return {
			btnCustomStyle: {
				width: '30vw',
				height: '5vh',
				borderRadius: '5px',
				marginTop: '3vh'
			},
			result: 'success',
			oilVariety: '',
			errMsg: '',
			orderAmount: ''
		}
	},
	/**
	 * 生命周期函数--监听页面加载
	 */
	onLoad: function(options) {
		const { status,orderAmount,oilVariety,errMsg } = options;
		this.result = status;
		this.oilVariety = oilVariety;
		this.errMsg = errMsg;
		this.orderAmount = orderAmount;
	},
	/**
	 * 生命周期函数--监听页面初次渲染完成
	 */
	onReady: function() {
	},
	/**
	 * 生命周期函数--监听页面显示
	 */
	onShow: function() {
	},
	/**
	 * 生命周期函数--监听页面隐藏
	 */
	onHide: function() {
	},
	/**
	 * 生命周期函数--监听页面卸载
	 */
	onUnload: function() {},
	/**
	 * 页面相关事件处理函数--监听用户下拉动作
	 */
	onPullDownRefresh: function() {},
	/**
	 * 页面上拉触底事件的处理函数
	 */
	onReachBottom: function() {},
	/**
	 * 用户点击右上角分享
	 */
	onShareAppMessage: function() {},
	methods: {
		jumpToCheckstand() {
			uni.reLaunch({
				url: '/pages/checkstand/checkstand'
			})
		},
		jumpToRecharge() {
			uni.navigateBack();
		}
	}
}
</script>

<style scoped>
@import './rechargeResult.css';
</style>