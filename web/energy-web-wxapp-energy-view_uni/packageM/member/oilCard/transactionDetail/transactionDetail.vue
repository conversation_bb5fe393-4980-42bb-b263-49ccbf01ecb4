<template>
	<view class="transaction-detail">
		<template>
			<view class="transaction-detail-header flex-column flex-center">
				<text>{{title}}</text>
				<text class="bold" style="padding-top: 5px;">{{alterAmountStr}}</text>
			</view>
			<template v-if="orderType === 'rechargePay'">
				<view class="transaction-detail-item flex-jc-between">
					<text class="color-grey">充值金额</text>
					<text>￥{{amount}}</text>
				</view>
				<view class="transaction-detail-item flex-jc-between">
					<text class="color-grey">优惠信息</text>
					<text>{{orderDesc}}</text>
				</view>
			</template>
			<template v-else>
				<view class="transaction-detail-item flex-jc-between">
					<text class="color-grey">消费金额</text>
					<text>￥{{amount}}</text>
				</view>
				<view class="transaction-detail-item flex-jc-between">
					<text class="color-grey">加油信息</text>
					<text>{{orderDesc}}</text>
				</view>
			</template>
			<view class="transaction-detail-item flex-jc-between">
				<text class="color-grey">付款方式</text>
				<text>{{payWay}}</text>
			</view>
			<view class="transaction-detail-item flex-jc-between">
				<text class="color-grey">交易时间</text>
				<text>{{createTime | timeFt}}</text>
			</view>
			<view class="transaction-detail-item flex-jc-between">
				<text class="color-grey">订单编号</text>
				<text>{{orderNo}}</text>
			</view>
		</template>
	</view>
</template>

<script>
import {queryOrderDetail} from '../../../api/1-oilCard'			
	
export default {
	data() {
		return {
			orderType: '',
			title: '',
			createTime: '',
			orderNo: '',
			amount: 0,
			status: 0,
			payWay: '',
			orderDesc: '',
			alterAmountStr: ''
		};
	},
	/**
	 * 生命周期函数--监听页面加载
	 */
	onLoad: function(options) {
		const {accountNo, orderType, orderNo} = options;
		
		this.orderType = orderType;
		
		let params = {
			accountNo,
			orderType,
			orderNo
		}
		this.queryOrderDetail(params);
	},
	/**
	 * 生命周期函数--监听页面初次渲染完成
	 */
	onReady: function() {},
	/**
	 * 生命周期函数--监听页面显示
	 */
	onShow: function() {},
	/**
	 * 生命周期函数--监听页面隐藏
	 */
	onHide: function() {},
	/**
	 * 生命周期函数--监听页面卸载
	 */
	onUnload: function() {},
	/**
	 * 页面相关事件处理函数--监听用户下拉动作
	 */
	onPullDownRefresh: function() {},
	/**
	 * 页面上拉触底事件的处理函数
	 */
	onReachBottom: function() {},
	/**
	 * 用户点击右上角分享
	 */
	onShareAppMessage: function() {},
	methods: {
		queryOrderDetail(params) {
			queryOrderDetail(params).then(res => {
				if(res.code===200) {
					return res.data;
				} else {
					uni.showToast({
					    title: res.msg,
					    icon: 'loading',
					    duration: 2000
					});
					
					throw new Error(res.msg);
				}
			}).then(data=> {
				const {createTime,orderNo,amount,status,payWay,orderDesc,title,alterAmountStr} = data;
				
				this.createTime = createTime;
				this.orderNo = orderNo;
				this.amount = amount;
				this.status = status;
				this.payWay = payWay;
				this.orderDesc = orderDesc;
				this.title = title;
				this.alterAmountStr = alterAmountStr;
			})
		}
	}
};
</script>

<style scoped>
@import './transactionDetail.css';
</style>
