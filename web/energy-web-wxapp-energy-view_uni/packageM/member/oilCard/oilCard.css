page {
    background-color: #fafafa;
}

.fz-24 {
	font-size: 24rpx;
}

.fz-36 {
	font-size: 36rpx;
}

.color-w {
	color: #fff;
}

.color-sp {
	color: #D2B999;
}

.bold {
	font-weight: 800;
}

.oil-card {
	position: relative;
	width: 100vw;
	height: 100vh;
	overflow: hidden;
}

.oil-card::after {
	width: 140%;
	height: 18vh;
	position: absolute;
	left: -20%;
	top: 0;
	z-index: -1;
	content: '';
	border-radius: 0 0 50% 50%;
	background-color: #EACA8E;;
}

.common-content {
	position: absolute;
	top: 7vh;
	left: 5vw;
	z-index: 9999;
}

.common-item {
	position: relative;
	width: 90vw;
	height: 24vh;
}

.common-item-img {
	position: absolute;
	top: 0;
	left: 0;
	z-index: 999;
	width: 100%;
	height: 100%;
}

.common-item-account {
	position: absolute;
	right: 3vw;
	top: 2vh;
	z-index: 9999;
}

.common-item-phone {
	position: absolute;
	left: 5vw;
	top: 6vh;
	z-index: 9999;
}

.common-item-amount {
	position: absolute;
	z-index: 9999;
}

.common-item-button {
	position: absolute;
	bottom: 0;
	z-index: 9999;
}

.nonactivated-img {
	width: 90vw;
}