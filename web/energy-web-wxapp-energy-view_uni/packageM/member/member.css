page {
    background-color: #F3F5F7;
}

.member-header {
	position: relative;
	width: 100vw;
	height: 21vh;
	background-color: #EACA8E;
	border-bottom-left-radius: 4vw;
	border-bottom-right-radius: 4vw;
}

.member-header-user {
	position: absolute;
	top: 3vh;
	left: 8vw;
}

.header-img {
	width: 88rpx;
	height: 88rpx;
	border-radius: 50%;
}

.user-desc {
	padding-left: 3vw;
	font-size: 32rpx;
	color: #222;
}

.user-desc-phone {
	font-size: 26rpx;
	color: #666;
}

.member-header-detail {
	position: absolute;
	bottom: 0;
	left: 4vw;
	height: 8.5vh;
}

.navigation-img {
	width: 92vw;
	height: 8.5vh;
	border-top-left-radius: 10rpx;
	border-top-right-radius: 10rpx;
}

.member-header-detail-item {
	position: absolute;
	bottom: 0;
	width: 100%;
	height: 100%;
}

.member-header-detail-item-title {
	font-size: 24rpx;
	color: #EFCF95;
}

.member-header-detail-item-desc {
	font-size: 30rpx;
	color: #fff;
	padding-bottom: 1vh;
}

.member-content {
	position: absolute;
	top: 23vh;
	width: 84vw;
	margin: 0 4vw;
	padding: 2vh 4vw;
	background-color: #fff;
	border-radius: 10px;
}

.member-content-title {
	font-size: 34rpx;
}

.member-content-entrance {
	font-size: 28rpx;
}

.member-content-entrance-item {
	width: 15vw;
	padding: 2vh 3vw;
}

.member-content-entrance-item-bigger {
	width: 18vw;
	padding: 2vh 5vw;
}

.content-img {
	width: 11vw;
	height: 11vw;
}
