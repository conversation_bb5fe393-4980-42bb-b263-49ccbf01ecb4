/* pages/info/integral/integral.wxss */
page {
    background-color: #fafafa;
	font-size: 36rpx;
}

.integral-container {
    min-height: 100vh;
}

.integral-header {
    padding: 3vh 0 6vh 0;
    border-bottom: 1px solid #666;
}

.integral-content {
	flex: 1;
}

.common {
    padding: 2vh 4vw;
    border-bottom: 1px solid #666;
}

.common-item:last-child {
    margin-bottom: 2vh;
}

.title {
    font-weight: bold;
}

.empty {
    width: 25vw;
}
