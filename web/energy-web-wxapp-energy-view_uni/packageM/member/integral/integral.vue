<template>
    <view class="integral-container flex-column">
        <view class="integral-header flex-column flex-center">
            <text>{{ points }}</text>
            <text>积分余额</text>
        </view>
        <view class="integral-content flex-column">
            <view class="common title flex-end flex-jc-between">
                <view class="flex-column">
                    <text>积分变动</text>
                    <text>变动原因</text>
                </view>
                <view>
                    <text>创建时间</text>
                </view>
            </view>
            <view v-if="pointsDetails.length > 0">
                <template v-for="(item, index) in pointsDetails">
                    <view class="common common-item flex-end flex-jc-between" :key="index">
                        <view class="flex-column">
                            <text>{{ item.alterBalance > 0 ? '+' : '' }}{{ item.alterBalance }}</text>
                            <text>{{ item.bizType | lightWeightPointsFt }}</text>
                        </view>
                        <view>
                            <text>{{ item.trxTime | timeFt }}</text>
                        </view>
                    </view>
                </template>
            </view>
            <view v-else class="flex-column flex-center" style="flex: 1">
                <image class="empty" mode="widthFix" src="../../static/integral/empty.png" />
            </view>
        </view>
    </view>
</template>
<script>
import { getInfo } from '../../api/0-member';

const app = getApp();
export default {
    data() {
        return {
            points: 0,
            pointsDetails: []
        };
    }
    /**
     * 生命周期函数--监听页面加载
     */,
    onLoad: function (options) {},
    /**
     * 生命周期函数--监听页面初次渲染完成
     */
    onReady: function () {},
    /**
     * 生命周期函数--监听页面显示
     */
    onShow: function () {
        this.getInfo();
    },
    /**
     * 生命周期函数--监听页面隐藏
     */
    onHide: function () {},
    /**
     * 生命周期函数--监听页面卸载
     */
    onUnload: function () {},
    /**
     * 页面相关事件处理函数--监听用户下拉动作
     */
    onPullDownRefresh: function () {},
    /**
     * 页面上拉触底事件的处理函数
     */
    onReachBottom: function () {},
    /**
     * 用户点击右上角分享
     */
    onShareAppMessage: function () {},
    methods: {
        getInfo: function () {
            const reqParams = {
                memberId: app.globalData.userInfo.memberId
            };
            getInfo(reqParams).then((res) => {
                if (res.code === 200) {
                    return res.data;
                } else {
                    uni.showToast({
                        title: res.msg,
                        icon: 'error',
                        duration: 2000
                    });
					
					throw new Error(res.msg);
                }
             }).then((data) => {
                const { points, pointsDetails } = data;
					
				this.points = points;
				this.pointsDetails = pointsDetails;
            });
        }
    }
};
</script>
<style>
@import './integral.css';
</style>
