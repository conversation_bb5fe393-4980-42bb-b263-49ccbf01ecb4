<script>
import { qryInfo } from './utils/baseInfo';
	
// app.js
export default {
	// 参考热启动、冷启动获取参数机制
	onLaunch(options) {
		// 记录进入小程序时间
		this.globalData.enterTime = Date.now();
		
		// #ifdef MP-WEIXIN
		// 1011:扫描二维码 1012:长按图片识别二维码 1013:扫描手机相册中选取的二维码 1035:公众号自定义菜单栏
		if (options.scene !== 1011 && options.scene !== 1012 && options.scene !== 1013 && options.scene !== 1035) {
			// 清空数据
			this.clearData();
			
			this.globalData.showError('请扫油站收款码进入！');
		}
		// #endif
		
		// #ifdef MP-ALIPAY
		// 1011:扫描二维码 , 这种情况则触发login
		if (options.scene !== '1011') {
			// 清空数据
			this.clearData();
			
			this.globalData.showError('请扫油站收款码进入！');
		}
		// #endif
		
		// const pcn = 'CYC_PCN_000190';
		const pcn = this.globalData.getPcn(options.query);
		if(pcn) {
			this.globalData.pcn = pcn;
			// 查询基础信息
			qryInfo(this.globalData).then(data=> {
				this.globalData.eventBus.emit('qryInfo');
			})
		}
		
		// 监听性能
		// if (wx.canIUse('getPerformance')) {
		//     const performance = wx.getPerformance()
		//     const performanceObserver = performance.createObserver((entryList) => {
		//         const entryArray = entryList.getEntries()
		//         entryArray.forEach((element) => {
		//             const {
		//                 name, duration, path, moduleName, navigationType,
		//             } = element
		//             duration && console.log(name, duration, {
		//                 path,
		//                 moduleName,
		//                 navigationType,
		//             })
		//         })
		//     })
		//     performanceObserver.observe({ entryTypes: ['navigation', 'render', 'script'] })
		// }
		
		this.globalData.autoUpdate();
	},
	onShow(options) {
		console.log(options, 'onShow');
		// 记录进入onShow的次数
		this.globalData.enterShowTimes += 1;
		
		const pcn = this.globalData.getPcn(options.query);
		if(pcn) {
			// 当重新扫码进入，重新请求基础信息
			if(this.globalData.enterShowTimes > 1) {
				// 记录重新进入小程序时间
				this.globalData.enterTime = Date.now();
				
				// 清空数据
				this.clearData();
				
				this.globalData.pcn = pcn;
				
				// 查询基础信息
				qryInfo(this.globalData).then(data=> {
					this.globalData.eventBus.emit('qryInfo');
				})
			}
		}
	},
	methods: {
		clearData: function() {
			this.globalData.userInfo.memberId = '';
			this.globalData.userInfo.paymentCodeInfoDTO = null;
			this.globalData.userInfo.oilCardAccount = null;
		}
	},
	globalData: {
		userInfo: {
			memberId: '',
			paymentCodeInfoDTO: null,
			oilCardAccount: null
		},
		enterShowTimes: 0, // 记录进入onShow的次数
		enterTime: 0, // 进入小程序时间
		activedTime: 300000, // 超过这个时间未执行支付操作则提示用户重新扫码进入
		provider: '',
		pcn: '',
		
		showError: function(msg) {
			uni.showModal({
				title: '提示',
				content: msg,
				showCancel: false,
				success: function() {
					// #ifdef MP-ALIPAY
					my.ap.openAlipayApp({
					  appCode: 'alipayScan',
					  success: res => {
					    console.log('openAlipayApp success', res);
					  },
					  fail: err => {
					    console.log('openAlipayApp fail', err);
					  }
					});
					// #endif
					
					// #ifdef MP-WEIXIN
					wx.exitMiniProgram();
					// #endif
				}
			});
		},
		// 版本更新
		autoUpdate: function() {
			let that = this; // 获取小程序更新机制兼容

			if (uni.canIUse('getUpdateManager')) {
				const updateManager = uni.getUpdateManager(); //1. 检查小程序是否有新版本发布

				updateManager.onCheckForUpdate(function(res) {
					console.log(res); // 请求完新版本信息的回调

					if (res.hasUpdate) {
						//2. 小程序有新版本，则静默下载新版本，做好更新准备
						updateManager.onUpdateReady(function() {
							uni.showModal({
								title: '更新提示',
								content: '新版本已经准备好，是否重启应用？',
								showCancel: false,

								// 变相强制更新
								success(res) {
									console.log(res);

									if (res.confirm) {
										//3. 新的版本已经下载好，调用 applyUpdate 应用新版本并重启
										updateManager.applyUpdate();
									} else if (res.cancel) {
										// 不需要强制更新的话这里不需要
										// wx.showModal({
										//   title: '温馨提示~',
										//   content: '本次版本更新涉及到新的功能添加，旧版本无法正常访问哦~',
										//   success: function (res) {
										//     // 第二次提示后，强制更新
										//     if (res.confirm) {
										//       // 新的版本已经下载好，调用 applyUpdate 应用新版本并重启
										//       updateManager.applyUpdate()
										//     } else if (res.cancel) {
										//       // 重新回到版本更新提示
										//       that.autoUpdate()
										//     }
										//   }
										// })
									}
								}
							});
						});
						updateManager.onUpdateFailed(function() {
							// 新的版本下载失败
							uni.showModal({
								title: '已经有新版本了哟~',
								content: '新版本已经上线啦~，请您删除当前小程序，重新搜索打开哟~'
							});
						});
					}
				});
			} else {
				uni.showModal({
					title: '提示',
					content: '当前微信版本过低，影响功能正常使用，请升级到最新微信版本后重试。'
				});
			}
		},
		getPcn: function(params) {
			let pcn;
			if (params) {
				if (params.q) {
					// 微信端扫普通二维码进入
					pcn = decodeURIComponent(params.q).split('=')[1];
				} else if (params.qrCode) {
					// 支付宝端扫普通二维码进入
					pcn = decodeURIComponent(params.qrCode).split('=')[1];
				} else if (params.scene) {
					// 从微信公众号进入
					pcn = params.scene;
				}
			}

			return pcn;
		},
		// 创建事件总线
		eventBus: {
			// 事件列表
			events: {},
			// 订阅事件
			on(event, callback) {
				console.log('on');
				if (!this.events[event]) {
					this.events[event] = [];
				}
				this.events[event].push(callback);
			},
			// 取消订阅
			off(event, callback) {
				if (this.events[event]) {
					const index = this.events[event].indexOf(callback);
					if (index !== -1) {
						this.events[event].splice(index, 1);
					}
				}
			},
			// 触发事件
			emit(event, ...args) {
				console.log('emit');
				if (this.events[event]) {
					this.events[event].forEach(callback => {
						callback(...args);
					});
				}
			}
		}
	}
};
</script>
<style lang="scss">
/* 注意要写在第一行，同时给style标签加入lang="scss"属性 */
@import 'uview-ui/index.scss';
@import './app.css';
</style>
