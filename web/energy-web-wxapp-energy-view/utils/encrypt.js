const Encrypt = require('./jsencrypt.js');
const CryptoJS = require('./crypto-js');

// RSA 公钥
const RsaSSPublicKey = 'MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA4n9JkkffVQvgYkBMBYJpXiXrhwuQyDFsHuOEkMOPflHf0cxIkPJKvaFDoU9JpFN6uIV1ksvlZ1vi539wzzIR+5iq5rWuvJZEBU1VxRAjwAoy+4XEXKRsWfg/CHkDqT9/PptFc8UOJuGc/9dQzlVU0KHzoHThTV24ryWmocdLcPven6iJWwUedCit7B1Fn4ZvD+ouAxXcauSRaAL1cpsI2FwzfIcb1KO+LoAu8ioqB6xJxp4oHmfbbH2Dt6We6T0RF8zMyDJfEGy1jZqgmhQps3cKBGCfEFdN5KGRHaDuvxuwJLZNoNFGNwnp3KHQ86AgimP4Qs36DRqN0K0+uNdf8wIDAQAB';

export const getRandomNum = () => {
  const chars = [
    '0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z', 'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z',
  ];
  let nums = '';
  // eslint-disable-next-line no-plusplus
  for (let i = 0; i < 16; i++) {
    // eslint-disable-next-line radix
    const id = parseInt(Math.random() * 61);
    nums += chars[id];
  }
  return nums;
};

// RSA加密 16位随机字符
export const encryptByRSA = function (randomNum) {
  const encrypt = new Encrypt.JSEncrypt({ default_key_size: 1024 });
  encrypt.setPublicKey(RsaSSPublicKey); // 设置rsa公钥
  return encrypt.encrypt(randomNum);
};

// AES加密
export const encryptByAES = function (aesVal, randomNum) {
  const key = CryptoJS.enc.Utf8.parse(randomNum);
  const val = CryptoJS.enc.Utf8.parse(JSON.stringify(aesVal));
  const encrypted = CryptoJS.AES.encrypt(val, key, {
    mode: CryptoJS.mode.ECB,
    padding: CryptoJS.pad.Pkcs7,
  });
  return encrypted.toString();
};

export const encryptOneParams = function (params) {
  const randomNum = getRandomNum();
  const encryptParams = encryptByAES(params, randomNum);
  const key = encryptByRSA(randomNum);
  return {
    value: encryptParams,
    key
  }
}

export default encryptOneParams;