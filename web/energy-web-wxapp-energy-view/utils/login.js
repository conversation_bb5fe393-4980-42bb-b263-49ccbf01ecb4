import { wxAuth } from '../api/0-checkstand';

const app = getApp();

export const login = () => {
  return new Promise((resolve,reject)=> {
    wx.login({
      success: (wxRes) => {
        console.log(wxRes, 'wx login');
        if (wxRes.code) {
          wxAuth({
            code: wxRes.code,
            pcn: app.globalData.userInfo.pcn
          }).then((res) => {
            if (res.code === 200) {
              return res.data;
            } else {
              wx.showToast({
                title: res.msg,
                icon: 'loading',
                duration: 2000
              });
            }
          }).then(({
            openId,
            unionId,
            memberId,
            token,
            paymentCodeInfoDTO
          }) => {
            app.globalData.TOKEN = token;
            app.globalData.userInfo.openId = openId;
            app.globalData.userInfo.unionId = unionId;
            app.globalData.userInfo.memberId = memberId;
            app.globalData.userInfo.paymentCodeInfoDTO = paymentCodeInfoDTO;

            wx.setStorageSync('token', token);

            if(memberId) {
              wx.setStorageSync('memberId', memberId);
            }

            resolve("success");
          })
        } else {
          wx.showToast({
            title: '微信授权失败',
            icon: 'error',
            duration: 2000,
          });
          console.error('微信授权失败' + wxRes.errMsg);

          reject("fail");
        }
      },
      fail: (err) => {
        console.log(err);

        reject("fail");
      }
    })
  }) 
};