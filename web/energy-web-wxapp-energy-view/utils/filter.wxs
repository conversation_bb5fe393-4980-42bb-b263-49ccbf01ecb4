// filter.wxs
var dateFormat = function (date) {
  var dates = date.split('/');
  return dates[0] + "年" + dates[1] + "月" + dates[2] + "日";
}
var timeFilter = function (time) {
  // 2020-06-21T07:00:01.000+0000
  return time.toString().split('.')[0].replace('T', ' ');
}
var moneyFormat = function (money) {
  return Number(money).toFixed(2);
}
var integerFormat = function (money) {
  return Number(money).toFixed(2);
}
var tradeListItemStateFilter = function (state) {
  var stateEnum = {
    '100': '支付成功',
    '101': '支付失败',
    '102': '其他',
  }
  return state ? stateEnum[(state).toString()] : '';
}
var illegalStatusFilter = function (status) {
  var statusEnum = {
    '0': '未处理',
    '1': '已处理',
    '2': '处理中',
    '9': '无需处理',
  }
  return status >= 0 ? statusEnum[(status).toString()] : '';
}
 
var trafficOrderStatusFilter = function (status) {
  var trafficOrderStatusEnum = {
    '100': '已完成',
    '101': '支付失败',
    '102': '待支付',
    '103': '办理中',
    '104': '退款中',
    '105': '已退款',
  }
  return status ? trafficOrderStatusEnum[(status).toString()] : '';
}
var orderImageFt = function (type) {
  var imageEnum = {
    'WEIXIN': '/images/trade/wxpay.svg',
    'ALIPAY': '/images/trade/alipay.svg',
    'CASH': '/images/trade/cash.svg',
    'THIRD_COOP_CHANNEL': '/images/trade/cash.svg',
    'BANK_CARD': '/images/trade/bankCard.svg',
    'refund': '/images/trade/refund.svg',
    'AUTO_REMIT_D0': '/images/trade/7day.svg',
    'AUTO_REMIT_SHIFT': '/images/trade/class.svg',
    'AUTO_REMIT_T0': '/images/trade/workDays.svg',
    'private': '/images/trade/private.svg',
    'public': '/images/trade/public.svg',
  }
  return imageEnum[type];
}
var orderTypeFt = function (type) {
  var typeEnum = {
    'WEIXIN': '微信支付',
    'ALIPAY': '支付宝支付',
    'CASH': '现金支付',
    'THIRD_COOP_CHANNEL': '第三方渠道',
    'BANK_CARD': '银行卡支付',
  }
  return typeEnum[type];
}
var batchFt = function (batchStatus) {
  var batchEnum = {
    '99': '已创建',
    '100': '处理中',
    '101': '处理完成',
    '102': '处理失败',
  };
  return batchEnum[batchStatus.toString()];
}
var locationFt = function (str) {
  if (str && str.length >= 18) {
    return str.slice(0, 18) + '...';
  } else {
    console.log(str === null)
    return str === null || str === 'null' ? '' : str;
  }
}
var distanceFt = function (dis) {
  if (dis >= 1000) {
    return (dis / 1000).toFixed(1) + 'km';
  } else {
    return Number(dis).toFixed(1) + 'm';
  }
}
var lightWeightPointsFt = function (bizType) {
  var lightWeightPointsEnum = {
    '1': '加油积分领取',
    '2': '兑换加油优惠',
    '3': '调账',
    '4': '扣减积分-重新发放',
    '5': '赠送积分-回退',
  };
  return lightWeightPointsEnum[bizType.toString()];
}

module.exports = {
  dateFormat: dateFormat,
  money: moneyFormat,
  orderStateFt: tradeListItemStateFilter,
  illegalStatusFt: illegalStatusFilter,
  trafficOrderStatusFt: trafficOrderStatusFilter,
  orderImageFt: orderImageFt,
  iconFt: orderImageFt,
  integerFt: integerFormat,
  orderTypeFt: orderTypeFt,
  timeFilter: timeFilter,
  batchFt: batchFt,
  locationFt: locationFt,
  distanceFt: distanceFt,
  lightWeightPointsFt: lightWeightPointsFt
}