<!--components/authorization/authorization.wxml-->
<view>
  <!-- 手机号授权弹窗 -->
  <van-dialog use-slot title="温馨提示" 
    show="{{ authStep === 0 }}" 
    show-cancel-button
    bind:cancel="closePhoneDialog"
    confirm-button-open-type="getPhoneNumber" 
    bind:getphonenumber="getPhoneNumber">
    <view class="flex-column flex-center">
      <image class="auth-logo" src="{{filePath ? filePath : '../../image/public/station-default-bg.png'}}" mode="aspectFill" lazy-load="false"/>
      <text class="auth-desc">注册后即可享受会员优惠</text>
    </view>
  </van-dialog>

  <!-- 用户信息授权弹窗 -->
  <van-dialog use-slot title="提示" 
    show="{{ authStep === 1 }}"
    show-cancel-button confirm-button-open-type="getUserInfo" 
    bind:cancel="closeUserDialog" 
    bind:getuserinfo="getUserInfo">
    <view class="flex-column flex-center">
      <text class="auth-user-desc">请完善您的用户信息！</text>
    </view>
  </van-dialog>
</view>
