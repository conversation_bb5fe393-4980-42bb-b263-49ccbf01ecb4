// components/authorization/authorization.js
import { newUserRegister } from '../../api/0-checkstand';

Component({
  /**
   * 组件的属性列表
   */
  properties: {
    mchNo: '',
    filePath: '', //油站缩列图
    authStep: 0 // 0: 手机号授权 1：用户信息授权
  },

  /**
   * 组件的初始数据
   */
  data: {
    isExisted: false, // 用户是否存在
  },

  /**
   * 组件的方法列表
   */
  methods: {
    closePhoneDialog() {
      this.triggerEvent('closePhoneDialog')
    },
    closeUserDialog() {
      this.triggerEvent('closeUserDialog')
    },
    getPhoneNumber: function(e) {
      const { errMsg, iv, encryptedData } = e.detail;
      if (errMsg === 'getPhoneNumber:fail user deny') {
        this.closePhoneDialog();
      } else if (errMsg === 'getPhoneNumber:ok') {
        const params = {
          iv,
          encryptedData,
          mchNo: this.properties.mchNo
        }
        console.log(params);
        newUserRegister(params).then((res) => {
          console.log(res);
          if (res.code === 200) {
            this.setData({
              isExisted: true
            });
            return res.data;
          } else {
            if (res.msg === 'Token不能为空' || res.msg === 'Token失效' || res.msg === '会员不存在') {
              // 清除缓存
              wx.clearStorageSync();
  
              wx.showToast({
                title: '请重新进入',
                icon: 'error',
                duration: 2000,
              }).then(res => {
                wx.redirectTo({
                  url: '/pages/index/index',
                })
              });
            } else {
              wx.showToast({
                title: res.msg,
                duration: 2000
              });
            }
          }
        }).then((data) => {
          if (data) {
            wx.setStorageSync('memberId', data);
          } else {
            wx.showToast({
              title: '请授权使用手机号',
              icon: 'error',
              duration: 2000
            });
          }

          this.triggerEvent('getPhoneNumber', {
            isExisted: this.data.isExisted,
          })
        })
      } else {
        console.error(errMsg);

        this.triggerEvent('getPhoneNumber', {
          isExisted: this.data.isExisted,
        })
      }
    },

    getUserInfo: function(e) {
      if(e.detail.errMsg === 'getUserProfile:fail auth deny') {
        this.closeUserDialog();
      } else if (e.detail.errMsg === 'getUserProfile:ok') {
        const { avatarUrl, nickName } = e.detail.userInfo;
        this.triggerEvent('getUserInfo', {
          avatarUrl: avatarUrl,
          nickName: nickName
        })
      }
    },
  }
})
