// pages/info/info.js
import { getInfo } from '../../api/1-info';
import { login } from '../../utils/login';

const app = getApp();

// 是否从公众号进入
const global = {
  isGzh: false
};

Page({

  /**
   * 页面的初始数据
   */
  data: {
    mchNo: '',
    filePath: '',
    showPhoneDialog: false,
    isDelay: false, // 当第一次授权手机号注册成功之后，延迟弹出用户信息授权弹窗
    showUserDialog: false,
    memberId: '',
    wxNickName: '',
    headImgUrl: '',
    phoneNo: '',
    points: 0,
    pointsDetails: []
  },

  wxLogin() {
    // 登录 
    login().then(res=> {
      if(res == 'success'){
        this.getLoginInfo();
      }
    });
  },

  getLoginInfo() {
    // 根据会员id操作
    const memberId = app.globalData.userInfo.memberId;
    const paymentCodeInfoDTO = app.globalData.userInfo.paymentCodeInfoDTO;
    if (memberId) {
      wx.setStorageSync('memberId', memberId);
      this.setData({
        memberId: memberId,
        showPhoneDialog: false,
      })

      const reqParams = {
        memberId: memberId
      }
      this.getInfo(reqParams);
    } else {
      this.setData({
        mchNo: paymentCodeInfoDTO.mchNo,
        filePath: paymentCodeInfoDTO.filePath,
        showPhoneDialog: true,
      })
    }
  },

  closePhoneDialog: function () {
    this.setData({
      showPhoneDialog: false
    })
  },

  getPhoneNumber: function (e) {
    console.log(e)
    const {
      isExisted
    } = e.detail;

    if (isExisted) {
      this.setData({
        isDelay: true,
        showPhoneDialog: false,
        memberId: wx.getStorageSync('memberId')
      })

      const reqParams = {
        memberId: wx.getStorageSync('memberId')
      }
      this.getInfo(reqParams);
    }
  },

  getInfo: function (params) {
    getInfo(params).then(res => {
      console.log(res)
      if (res.code === 200) {
        return res.data;
      } else {
        if (res.msg === '会员不存在' || res.msg === 'memberId不能为空' ||
          res.msg === 'Token不能为空' || res.msg === 'Token失效') {
          // 先清除缓存
          wx.clearStorageSync();

          if (global.isGzh) {
            this.wxLogin();
          } else {
            wx.navigateBack({
              delta: 1
            })
          }
        } else {
          wx.showToast({
            title: res.msg,
            icon: 'error',
            duration: 2000
          });
        }
      }
    }).then(data => {
      const {
        headImgUrl,
        wxNickName,
        phoneNo,
        points,
        pointsDetails
      } = data;

      this.setData({
        wxNickName: wxNickName,
        headImgUrl: headImgUrl,
        phoneNo: phoneNo,
        points: points,
        pointsDetails: pointsDetails,
        showUserDialog: this.data.isDelay || wxNickName || headImgUrl ? false : true
      })
    })
  },

  getUserInfo: function (e) {
    const {
      avatarUrl,
      nickName
    } = e.detail;

    const reqParams = {
      memberId: this.data.memberId,
      wxNickName: nickName,
      headImgUrl: avatarUrl
    }
    this.getInfo(reqParams);
  },

  closeUserDialog: function () {
    this.setData({
      showUserDialog: false
    })
  },

  jumpToIntegral: function () {
    wx.navigateTo({
      url: '/pages/info/integral/integral',
    })
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    // 判断是否带有参数，有参数说明是从公众号会员中心菜单栏进入
    if (options.scene) {
      // 清除缓存
      wx.clearStorageSync();
      
      app.globalData.userInfo.pcn = options.scene;
      global.isGzh = true;
    }
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    // 根据会员id获取会员信息，有昵称信息则不弹窗授权，有则弹窗授权
    this.setData({
      memberId: wx.getStorageSync("memberId"),
      isDelay: false
    })

    const reqParams = {
      memberId: this.data.memberId
    }
    this.getInfo(reqParams);
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {

  }
})