<!--pages/info/info.wxml-->
<view class="info-container flex-column">
  <view class="info-header">
    <image class="info-header-bg" mode="widthFix" src="../../image/info/info-bg.png" />
    <view class="header-body">
      <view class="header-body-item flex-jc-between">
        <text>会员ID：{{memberId}}</text>
        <text>积分：{{points}}分</text>
      </view>
      <view class="flex-column flex-center">
        <image class="header-img" src="{{ headImgUrl ? headImgUrl : '../../image/info/default-header.png'}}" mode="aspectFill" lazy-load="false" binderror="" bindload="" />
        <text class="common-item">{{wxNickName ? wxNickName : '微信用户'}}</text>
        <text class="common-item">{{phoneNo}}</text>
      </view>
    </view>
  </view>
  <view class="info-body">
    <view class="info-body-actions flex-center" bindtap="jumpToIntegral">
      <image src="../../image/info/credit.png" />
      <text class="title">积分明细</text>
    </view>
  </view>
</view>

<view wx:if="{{showUserDialog}}">
  <my-authorization authStep="{{1}}" bindcloseUserDialog="closeUserDialog" bindgetUserInfo="getUserInfo"></my-authorization>
</view>

<view wx:if="{{showPhoneDialog}}">
  <my-authorization mchNo="{{mchNo}}" authStep="{{0}}" filePath="{{filePath}}" bindclosePhoneDialog="closePhoneDialog" bindgetPhoneNumber="getPhoneNumber"></my-authorization>
</view>