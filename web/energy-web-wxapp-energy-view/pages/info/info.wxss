/* pages/info/info.wxss */
page {
  background-color: #FAFAFA;
}

.info-header-bg {
  width: 100vw;
  background-size: 100vw 33vh;
}

.header-body {
  position: absolute;
  top: 8vh;
  left: 12vw;
  right: 12vw;
  color: #B46827;
}

.header-body-item {
  font-size: 28rpx;
}

.header-img {
  width: 15vw;
  height: 15vw;
  margin-top: 3vh;
  border-radius: 50%;
}

.common-item {
  padding: 5rpx 0;
}

.info-body {
  background-color: #FAFAFA;
}

.info-body-actions {
  height: 8vh;
  margin: 2vh 4vw;
  background:rgba(255,255,255,1);
  box-shadow:4px 3px 13px 0px rgba(0, 0, 0, 0.1);
  border-radius:6px;
}

.info-body-actions image {
  width: 6vw;
  height: 6vw;
  margin-right: 4vw;
}

.info-body-actions .title {
  font-size: 1.3rem;
  line-height: 1;
}

.auth-desc {
  margin: 2vh 0;
}