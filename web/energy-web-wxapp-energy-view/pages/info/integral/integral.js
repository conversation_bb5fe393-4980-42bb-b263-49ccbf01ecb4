// pages/info/integral/integral.js
import {
  getInfo
} from '../../../api/1-info';

Page({

  /**
   * 页面的初始数据
   */
  data: {
    points: 0,
    pointsDetails: []
  },

  getInfo: function() {
    const reqParams = {
      memberId: wx.getStorageSync("memberId")
    }

    getInfo(reqParams).then(res=> {
      if (res.code === 200) {
        return res.data;
      } else {
        if (res.msg === '会员不存在' || res.msg === 'memberId不能为空'
              || res.msg === 'Token不能为空' || res.msg === 'Token失效') {
          // 先清除缓存
          wx.clearStorageSync();
          
          wx.navigateBack({
            delta: 2
          })
        } else {
          wx.showToast({
            title: res.msg,
            icon: 'error',
            duration: 2000
          });
        }
      }
    }).then(data=> {
      const {
        points,
        pointsDetails
      } = data;

      this.setData({
        points: points,
        pointsDetails: pointsDetails
      })
    })
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {

  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    this.getInfo();
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {

  }
})