<!--pages/info/integral/integral.wxml-->
<wxs src="../../../utils/filter.wxs" module="FT" />

<view class="integral-container flex-column">
  <view class="integral-header flex-column flex-center">
    <text>{{points}}</text>
    <text>积分余额</text>
  </view>
  <view class="flex-column" style="flex: 1;">
    <view class="common title flex flex-jc-between">
      <view class="flex-column">
        <text>积分变动</text>
        <text>变动原因</text>
      </view>
      <view class="flex-end">
        <text>创建时间</text>
      </view>
    </view>
    <view wx:if="{{pointsDetails.length>0}}">
      <block wx:for="{{pointsDetails}}" wx:key="item">
        <view class="common common-item flex flex-jc-between">
          <view class="flex-column">
            <text>{{item.alterBalance>0 ? '+' : ''}}{{item.alterBalance}}</text>
            <text>{{FT.lightWeightPointsFt(item.bizType)}}</text>
          </view>
          <view class="flex-end">
            <text>{{FT.timeFilter(item.trxTime)}}</text>
          </view>
        </view>
      </block>
    </view>
    <view wx:else class="flex-column flex-center" style="flex: 1;">
      <image class="empty" mode="widthFix" src="../../../image/integral/empty.png" />
    </view>
  </view>
</view>