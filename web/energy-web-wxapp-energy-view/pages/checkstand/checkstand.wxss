/* pages/checkstand/checkstand.wxss */
page {
  background-color: #FAFAFA;
}

.checkstand-header {
  background-color: #FAFAFA;
  border-radius: 20rpx;
  box-shadow: 0.125rem 0.25rem 0.375rem rgba(0, 0, 0, .1);
  padding: 1vh 4vw 2vh 4vw;
  margin: 1vh 2vw 2vh 2vw;
}

.checkstand-header-item {
  position: relative;
}

.checkstand-header-image {
  width: 18vw;
  height: 18vw;
  margin: 2vw;
  margin-right: 5vw;
  border-radius: 10rpx;
  border: 1px solid #ccc;
}

.member {
  position: absolute;
  top: 0;
  right: 0;
  font-size: 25rpx;
  color: #00BFBF;
}

.member-image {
  width: 8vw;
  height: 8vw;
}

.checkstand-header-info {
  padding: 1vh 0;
  justify-content: space-between;
}

.checkstand-header-title {
  font-size: 34rpx;
  line-height: 38rpx;
  font-weight: 600;
  color: #333;
  width: 45vw;
}

.current-title {
  width: 12vw;
  height: 12vw;
  color: #fd0000;
  font-size: 30rpx;
  font-weight: 800;
  line-height: 34rpx;
  letter-spacing: 2rpx;
  border: 5rpx solid #fd0000;
  border-radius: 15%;
  margin-right: 10vw;
}

.checkstand-prices-board {
  margin: 2vh 2vw 0 2vw;
  border-top: 1px solid #E2E2E2;
  padding-top: 2vh;
}

.swiper-item-content {
  height: 100%;
}

.swiper-item-content-padding {
  padding: 1vw 0;
  text-align: center;
}

.swiper-item-title {
  color: #999;
  font-size: 25rpx;
}

.swiper-item-desc {
  font-size: 30rpx;
}

.station-location {
  font-size: 22rpx;
  line-height: 30rpx;
  
}

.distance-text {
  font-size: 22rpx;
  line-height: 30rpx;
  color: #999;
}

.content {
  margin-bottom: 16vh;
}

.common-selector {
  transition: max-height 0.25s ease-in-out;
  transition: font-size 0.25s ease-in-out;
  font-size: 32rpx;
}

.oilNo-selector {
  padding: 0 2.5vw;
}

.money-selector {
  padding: 0 2vw;
}

.oilNo-item {
  margin: 1vh 1.5vw;
  padding: 1vw 2vw;
  border-radius: 10rpx;
  width: 16vw;
  text-align: center;
}

.select-active {
  background-color: rgba(252, 193, 0, 0.1);
  color: #fcc100;
  border: 1px solid #fcc100;
}

.select-noactive {
  color: #333;
  border: 1px solid #ccc;
}

.bold-title {
  margin: 1vh 4vw;
  font-weight: bolder;
}

.money-input {
  width: 100vw;
}

.money-input-item {
  margin: 0 4vw;
}

.money-input .van-cell {
  border: 1px solid #ccc;
  border-radius: 10rpx;
}

.money-input .cell-active .van-cell{ 
  border: 1px solid #fcc100;
  background-color: rgba(252, 193, 0, 0.1);
}

.money-input .van-field__control {
  text-align: center;
  font-size: 38rpx;
  font-weight: 800;
}

.money-item {
  margin: 1vh 2vw;
  padding: 1vw 2vw;
  border-radius: 10rpx;
  width: 24vw;
  height: 5vh;
  text-align: center;
}

.preferential-price {
  font-size: 25rpx;
  color: #E70000;
}

.integral-deduction {
  margin-top: 2vh;
  padding-bottom: 15vh;
}

.integral-deduction-item {
  color: #333;
  font-size: 30rpx;
  border: 1px solid #ccc;
  border-radius: 10rpx;
  margin: 0 4vw;
  padding: 2vh 2vw;
}

.integral-deduction-item-title {
  font-weight: bold;
  border-bottom: 1px solid #ccc;
  padding-bottom: 1vh;
  margin-bottom: 2vh;
}

.custom-label {
  text-align: right;
  padding: 0;
  margin: 0;
}

.submit-bar-class .van-submit-bar {
  border-top: 1px solid #ccc;
  background: #FAFAFA;
  box-shadow: 0px 0px 16px 0px rgba(0, 0, 0, 0.17);
}

.submit-bar-info {
  flex: 1;
}

.submit-bar-info .pay-money-box {
  font-size: 25rpx;
  font-weight: bold;
  color: #333;
}

.submit-bar-info .pay-money-box .pay-money-box-discount {
  color: #E70000;
  padding-left: 2vw;
}

.submit-bar-info .pay-detail-box {
  font-size: 30rpx;
  padding-right: 2vw;
  color: #666;
}

.submit-bar-class .van-submit-bar__bar {
  padding-left: 15rpx;
  padding-right: 0;
}

.submit-bar-class .van-submit-bar__button {
  height: 100%;
}

.submit-bar-class .van-button {
  height: 100%;
  border-radius: 0;
  border: none;
  background-color: #E70000;
  color: #fff;
}

.overlay-content {
  position: absolute;
  bottom: calc(100rpx + env(safe-area-inset-bottom));
  width: 94vw;
  padding: 2vh 3vw;
  background-color: #AAAAAA;
  font-size: 30rpx;
  color: #fff;
}

.line {
  width: 45%;
  border-top:1px #fff solid;
}

.text-align-center {
  text-align: center;
}

.overlay-content-detail {
  width: 20vw;
  font-size: 28rpx;
}

.auth-logo {
  width: 30vw;
  height: 30vw;
  border-radius: 50%;
  margin: 2vh 0;
}

.auth-desc {
  color: #666;
  font-size: 26rpx;
}