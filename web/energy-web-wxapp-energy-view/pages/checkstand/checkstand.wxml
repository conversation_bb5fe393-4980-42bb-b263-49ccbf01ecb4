<!--pages/checkstand/checkstand.wxml-->
<wxs src="../../utils/filter.wxs" module="FT" />

<view>
  <view class="checkstand-header">
    <view class="flex checkstand-header-item">
      <image class="checkstand-header-image" src="{{filePath ? filePath : '../../image/public/station-default-bg.png'}}" mode="aspectFill" lazy-load="false" binderror="" bindload="" />
      <view class="flex-column checkstand-header-info">
        <text class="checkstand-header-title">{{ mchName }}</text>
        <text class="distance-text">{{ address }}</text>
      </view>
      <view class="member flex-column flex-center" wx:if="{{openLightweightMember}}" bind:tap="jumpToInfo">
        <image class="member-image" src="../../image/checkstand/member.png" mode="aspectFill" lazy-load="false" binderror="" bindload="" />
        <text>会员中心</text>
      </view>
    </view>
    <view class="flex checkstand-prices-board">
      <view class="current-title flex-column flex-center">
        <text>今日</text>
        <text>油价</text>
      </view>
      <view style="flex-grow: 1;">
        <swiper style="height: 100%" indicator-dots="{{indicatorDots}}" autoplay="{{autoplay}}" interval="{{interval}}" vertical="{{vertical}}" circular="{{circular}}" duration="{{duration}}" current="{{currIndex}}">
          <block wx:for="{{activeOilList.length>0 ? activeOilList : oilList}}" wx:key="item">
            <swiper-item>
              <view class="swiper-item-content flex-jc-between">
                <view class="swiper-item-content-padding flex-column flex-jc-between">
                  <text class="swiper-item-title">油品</text>
                  <text class="swiper-item-desc">{{item.oilTypeDesc}}</text>
                </view>
                <view class="swiper-item-content-padding flex-column flex-jc-between">
                  <text class="swiper-item-title">{{openLightweightMember ? '会员价' : '优惠价'}}</text>
                  <text class="swiper-item-desc">{{FT.money(item.memberPrice)}}</text>
                </view>
                <view class="swiper-item-content-padding flex-column flex-jc-between">
                  <text class="swiper-item-title">油站价</text>
                  <text class="swiper-item-desc">{{FT.money(item.gunPrice)}}</text>
                </view>
                <view class="swiper-item-content-padding flex-column flex-jc-between">
                  <text class="swiper-item-title">指导价</text>
                  <text class="swiper-item-desc">{{FT.money(item.nationalStandardPrice)}}</text>
                </view>
              </view>
            </swiper-item>
          </block>
        </swiper>
      </view>
    </view>
  </view>
  <view class="content flex-column">
    <!-- 油枪 -->
    <view class="flex-column" wx:if="{{sysProduct === 'OIL'}}">
      <view class="flex-jc-between flex-end">
        <text class="bold-title">选择油枪</text>
        <text style="font-size: 26rpx;line-height: 26rpx;margin: 1vh 4vw;">请务必与加油员确认枪号</text>
      </view>
      <view class="flex flex-wrap common-selector oilNo-selector">
        <block wx:for="{{ gunNoList }}" wx:key="index">
          <view class="oilNo-item flex-column flex-center {{ activeGunNo === item.gunNo ? 'select-active' : 'select-noactive' }}" bind:tap="selectGunNo" data-item="{{ item }}">
            <text style="font-size: 32rpx; line-height: 32rpx; padding-bottom: 6rpx;">{{ item.gunNo }}号</text>
            <text style="font-size: 24rpx; line-height: 24rpx; color: #999;">{{ item.oilTypeDesc }}</text>
          </view>
        </block>
      </view>
    </view>

    <!-- 金额 -->
    <view class="flex-column">
      <text class="bold-title">输入金额</text>
      <view class="flex-center" wx:if="{{sysProduct === 'OIL'}}">
        <view class="money-input" bind:tap="clickInput">
          <view class="money-input-item {{isDisabled ? '' : 'cell-active'}}">
            <van-field disabled="{{isDisabled}}" auto-focus="{{true}}" value="{{ inputAmount }}" type="digit" bind:input="changeAmount" bind:blur="confirmAmount" placeholder-style="text-align: center" placeholder="请填写金额" use-right-icon-slot>
              <text slot="right-icon" style="color: #333;">约{{oilLiters}}升</text>
            </van-field>
          </view>
          <view class="flex common-selector money-selector">
            <block wx:for="{{ preferentialPriceTemplateDTOList }}" wx:key="templateAmount">
              <view class="money-item flex-column flex-center {{ activeMoney === item.templateAmount ? 'select-active' : 'select-noactive' }}" bind:tap="selectMoney" data-money="{{ item.templateAmount }}">
                <view>{{ item.templateAmount }}</view>
              </view>
            </block>
          </view>
        </view>
      </view>
      <view class="flex-center" wx:if="{{sysProduct === 'NON_OIL'}}">
        <view class="money-input">
          <view class="money-input-item cell-active">
            <van-field value="{{ inputAmount }}" auto-focus="{{true}}" type="digit" bind:input="changeAmount" bind:blur="confirmAmount" placeholder-style="text-align: center" placeholder="请填写金额">
            </van-field>
          </view>
        </view>
      </view>
    </view>

    <!-- 积分抵扣 -->
    <view class="integral-deduction" wx:if="{{openLightweightMember && usedPointsResult.usablePoints}}">
      <view class="integral-deduction-item flex-column">
        <text class="integral-deduction-item-title">积分抵扣</text>
        <view class="flex-jc-between">
          <text>可用{{usedPointsResult.usablePoints}}积分抵扣</text>
          <view class="flex-jc-between">
            <text>- ￥ {{FT.money(usedPointsResult.usablePointsDeductAmount)}}</text>
            <van-checkbox value="{{ checked }}" bind:change="changeCheck" checked-color="red" style="width: 20px;height: 20px;margin-left: 2vw;"></van-checkbox>
          </view>
        </view>
      </view>
    </view>
  </view>
  <van-submit-bar class="submit-bar-class" button-text="支付" bind:submit="executePay" tip="{{ true }}" loading="{{ submitLoading }}">
    <view class="submit-bar-info flex-jc-between flex-end">
      <view class="pay-money-box">
        <text>实付:￥<text style="font-size: 40rpx;">{{orderFinalAmount}}</text></text>
        <text wx:if="{{actualMarketingAmount>0}}" class="pay-money-box-discount">已优惠￥{{actualMarketingAmount}}</text>
      </view>
      <view wx:if="{{ sysProduct === 'OIL' && orderFinalAmount > 0 }}" class="pay-detail-box" bind:tap="clickDetail">明细</view>
    </view>
  </van-submit-bar>
</view>

<van-overlay show="{{ showOverlay }}" bind:click="clickHide" z-index="{{ 10 }}">
  <view class="overlay-content">
    <view class="flex-jc-between flex-center">
      <text class="overlay-content-detail text-align-center">加油金额</text>
      <text class="line"></text>
      <text class="overlay-content-detail">{{ FT.money(inputAmount) }}元</text>
    </view>
    <view class="flex-jc-between flex-center">
      <text class="overlay-content-detail text-align-center">加油升数</text>
      <text class="line"></text>
      <text class="overlay-content-detail">约{{ FT.money(oilLiters) }}升</text>
    </view>
    <view class="flex-jc-between flex-center">
      <view class="overlay-content-detail flex-column text-align-center">
        <text>优惠金额</text>
        <text>(含服务费)</text>
      </view>
      <text class="line"></text>
      <text class="overlay-content-detail">{{ FT.money(actualMarketingAmount) }}元</text>
    </view>
    <view class="flex-jc-between flex-center">
      <text class="overlay-content-detail text-align-center">实付金额</text>
      <text class="line"></text>
      <text class="overlay-content-detail">{{ FT.money(orderFinalAmount) }}元</text>
    </view>
  </view>
</van-overlay>

<view wx:if="{{openLightweightMember && showPhoneDialog}}">
  <my-authorization mchNo="{{mchNo}}" authStep="{{0}}" filePath="{{filePath}}" bindclosePhoneDialog="closePhoneDialog" bindgetPhoneNumber="getPhoneNumber"></my-authorization>
</view>