// pages/checkstand/checkstand.js
import { qryPcn,wxPay,computeOrder } from '../../api/0-checkstand';
import { login } from '../../utils/login';

const app = getApp();

Page({

  /**
   * 页面的初始数据
   */
  data: {
    background: [{
      imageURL: '../../image/checkstand/banner-01.png'
    }],
    indicatorDots: false,
    autoplay: true,
    interval: 2000,
    duration: 500,
    pcn: '',
    submitLoading: false,
    showOverlay: false,
    address: '',
    sysProduct: '',
    filePath: '',
    fullName: '',
    mchName: '',
    mchNo: '',
    oilList: [],
    activeOilList: [],
    gunNoList: [],
    preferentialPriceTemplateDTOList: [],
    activeGunNo: -1,
    activeMoney: -1,
    isDisabled: true,
    oilNo: '',
    gunNo: '',
    selGunPrice: 0,
    selMemberPrice: 0,
    selNationalStandardPrice: 0,
    inputAmount: '',
    oilLiters: 0,
    otherAmount: 0, //非油品金额（暂固定传0）
    usedPointsResult: null,
    directDiscount: 0, //直降金额
    platServiceFee: 0, //服务费
    actualMarketingAmount: 0, //最终优惠金额
    orderFinalAmount: 0, //最终订单金额
    openLightweightMember: false, //是否开通会员体系
    isExisted: false, //判断用户是否存在
    canPay: false, //取消注册会员也能支付
    showPhoneDialog: false, //展示手机号授权框
    checked: false, //是否勾选积分扣减
    vertical: true,
    circular: true,
    currIndex: 0, //swiper
  },

  jumpToInfo() {
    if (this.data.isExisted) {
      wx.navigateTo({
        url: '/pages/info/info'
      })
    } else {
      this.setData({
        showPhoneDialog: true
      })
    }
  },

  wxLogin() {
    // 登录 
    login().then(res => {
      if (res == 'success') {
        // 根据会员id操作
        const memberId = app.globalData.userInfo.memberId;
        if (memberId) {
          this.setData({
            isExisted: true
          })
        } else {
          this.setData({
            isExisted: false
          })
        }

        this.qryPcn();
      }
    });
  },

  qryPcn: function () {
    const reqParams = {
      pcn: this.data.pcn
    }

    qryPcn(reqParams).then((res) => {
      if (res.code === 200) {
        return res.data;
      } else {
        if (res.msg === 'Token不能为空' || res.msg === 'Token失效') {
          // 清除缓存
          wx.clearStorageSync();

          this.wxLogin();
        } else {
          wx.showToast({
            title: res.msg,
            icon: 'error',
            duration: 2000
          }).then(res => {
            wx.navigateTo({
              url: '/pages/index/index',
            })
          });
        }
      }
    }).then((data) => {
      console.log(data, 'qryPcn')

      if(data) {
        const {
          address,
          filePath,
          fullName,
          mchName,
          mchNo,
          oilList,
          gunNoList,
          sysProduct,
          openLightweightMember
        } = data;
  
        this.setData({
          address: address,
          filePath: filePath,
          fullName: fullName,
          mchName: mchName,
          mchNo: mchNo,
          oilList: oilList,
          gunNoList: gunNoList,
          sysProduct: sysProduct,
          openLightweightMember: openLightweightMember,
          activeOilList: [],
          currIndex: 0,
          oilNo: '',
          activeGunNo: -1,
          gunNo: '',
          preferentialPriceTemplateDTOList: [],
          selGunPrice: '',
          selMemberPrice: '',
          selNationalStandardPrice: '',
          inputAmount: '',
          isDisabled: true,
          activeMoney: -1,
          oilLiters: 0,
          otherAmount: 0,
          directDiscount: 0,
          orderFinalAmount: 0,
          platServiceFee: 0,
          actualMarketingAmount: 0,
          checked: false,
          usedPointsResult: null
        })
      }
    })
  },

  selectGunNo: function (e) {
    const {
      oilType,
      gunNo,
      gunPrice,
      memberPrice,
      nationalStandardPrice,
      preferentialPriceTemplateDTOList
    } = e.currentTarget.dataset.item;

    let currArr = [];
    let currOil = this.data.oilList.find(e => e.oilType === oilType);
    currArr.push(currOil);
    
    this.setData({
      activeOilList: currArr,
      currIndex: 0,
      oilNo: oilType,
      activeGunNo: gunNo,
      gunNo: gunNo,
      preferentialPriceTemplateDTOList: preferentialPriceTemplateDTOList,
      selGunPrice: gunPrice,
      selMemberPrice: memberPrice,
      selNationalStandardPrice: nationalStandardPrice,
      inputAmount: '',
      isDisabled: false,
      activeMoney: -1,
      oilLiters: 0,
      otherAmount: 0,
      directDiscount: 0,
      orderFinalAmount: 0,
      platServiceFee: 0,
      actualMarketingAmount: 0,
      checked: false,
      usedPointsResult: null
    })
  },

  changeAmount: function (e) {
    this.setData({
      inputAmount: e.detail,
      activeMoney: -1,
      checked: false,
      usedPointsResult: null
    })
  },

  selectMoney: function (e) {
    const {
      money
    } = e.currentTarget.dataset;

    if (this.data.isDisabled) {
      wx.showToast({
        title: '请先选择油枪',
        icon: 'error',
        duration: 2000,
      });

      return;
    }

    this.setData({
      activeMoney: money,
      inputAmount: money,
      checked: false,
      usedPointsResult: null
    });

    this.computeFinal();
  },

  clickInput: function () {
    if (this.data.isDisabled) {
      wx.showToast({
        title: '请先选择油枪',
        icon: 'error',
        duration: 2000,
      });
    }
  },

  confirmAmount: function (e) {
    console.log("blur: " + this.data.inputAmount);
    if (this.data.inputAmount) {
      this.computeFinal();
    } else {
      this.initData();
    }
  },

  computeFinal: function () {
    let reg = /^(\d+)(.\d{0,2})?$/
    if (this.data.inputAmount && !reg.test(this.data.inputAmount)) {
      wx.showToast({
        title: '输入金额有误',
        icon: 'error',
        duration: 2000,
      }).then(res => {
        this.initData();
      })
      return;
    }

    if (this.data.sysProduct === 'OIL') {
      // 输入金额
      if ((!this.data.inputAmount && !this.data.otherAmount) || this.data.inputAmount <= 0) {
        this.setData({
          inputAmount: ''
        })
        this.initData();
        return;
      }

      const reqParam = {
        oilAmount: this.data.inputAmount,
        gunNo: this.data.activeGunNo,
        mchNo: this.data.mchNo,
        otherAmount: this.data.otherAmount,
        memberId: app.globalData.userInfo.memberId || wx.getStorageSync("memberId")
      }

      computeOrder(reqParam).then((res) => {
        if (res.code === 200) {
          return res.data;
        } else {
          if (res.msg === 'Token不能为空' || res.msg === 'Token失效' || res.msg === '会员不存在') {
            // 清除缓存
            wx.clearStorageSync();

            this.jumpToIndex();
          } else {
            wx.showToast({
              title: res.msg,
              icon: 'error',
              duration: 2000,
            });
          }
        }
      }).then((data) => {
        const {
          oilLiters,
          directDiscount,
          orderFinalAmount,
          usedPointsResult,
          platServiceFee,
          actualMarketingAmount
        } = data;

        this.setData({
          originalResult: data,
          oilLiters: oilLiters,
          directDiscount: directDiscount,
          orderFinalAmount: orderFinalAmount,
          usedPointsResult: usedPointsResult,
          platServiceFee: platServiceFee,
          actualMarketingAmount: actualMarketingAmount
        })
      })
    } else {
      this.setData({
        orderFinalAmount: this.data.inputAmount ? Number(this.data.inputAmount) : 0,
        otherAmount: this.data.inputAmount ? Number(this.data.inputAmount) : 0
      })
    }
  },

  beforeExecutePay() {
    return new Promise((resolve, reject) => {
      if (this.data.sysProduct === 'OIL') {
        if (!this.data.gunNo) {
          wx.showToast({
            title: '请选择油枪号',
            icon: 'error',
            duration: 2000
          });
          reject();
        } else if (this.data.inputAmount <= 0) {
          console.log(this.data.inputAmount)
          wx.showToast({
            title: '请输入金额',
            icon: 'error',
            duration: 2000,
          });
          reject();
        }
      } else {
        if (this.data.inputAmount <= 0) {
          wx.showToast({
            title: '请输入金额',
            icon: 'error',
            duration: 2000,
          });
          reject();
        }
      }
      resolve();
    });
  },

  executePay() {
    console.log('wechat pay...');
    this.setData({
      submitLoading: true
    });

    wx.showToast({
      title: '支付中',
      icon: 'loading',
      duration: 60000
    });

    let params = {};
    if (this.data.sysProduct === 'OIL') {
      params = {
        mchNo: this.data.mchNo,
        gunNo: this.data.gunNo,
        oilType: this.data.oilNo,
        oilsSingleAmount: this.data.selGunPrice,
        oilsDiscountAmount: this.data.selMemberPrice,
        oilsForecastLiters: this.data.checked ? this.data.usedPointsResult.oilLiters : this.data.oilLiters,
        oilsDirectDiscount: this.data.checked ? this.data.usedPointsResult.directDiscount : this.data.directDiscount,
        oilOriginalAmount: this.data.inputAmount,
        totalDiscountAmount: this.data.directDiscount,
        orderAmount: this.data.checked ? this.data.usedPointsResult.orderFinalAmount : this.data.orderFinalAmount,
        sysProduct: this.data.sysProduct,
        otherAmount: this.data.otherAmount,
        memberId: app.globalData.userInfo.memberId || wx.getStorageSync("memberId"),
        points: this.data.checked ? this.data.usedPointsResult.usablePoints : 0,
        pointsDeductAmount: this.data.checked ? this.data.usedPointsResult.usablePointsDeductAmount : 0,
        platServiceFee: this.data.checked ? this.data.usedPointsResult.platServiceFee : this.data.platServiceFee,
        paymentCodeNo: this.data.pcn
      };
    } else {
      params = {
        mchNo: this.data.mchNo,
        oilsSingleAmount: 0,
        oilsDiscountAmount: 0,
        oilsForecastLiters: 0,
        oilsDirectDiscount: 0,
        oilOriginalAmount: 0,
        totalDiscountAmount: 0,
        orderAmount: this.data.checked ? this.data.usedPointsResult.orderFinalAmount : this.data.orderFinalAmount,
        sysProduct: this.data.sysProduct,
        otherAmount: this.data.otherAmount,
        memberId: app.globalData.userInfo.memberId || wx.getStorageSync("memberId"),
        points: this.data.checked ? this.data.usedPointsResult.usablePoints : 0,
        pointsDeductAmount: this.data.checked ? this.data.usedPointsResult.usablePointsDeductAmount : 0,
        platServiceFee: this.data.checked ? this.data.usedPointsResult.platServiceFee : this.data.platServiceFee,
        paymentCodeNo: this.data.pcn
      };
    }

    this.beforeExecutePay().then(() => {
      // 先判断是否开通会员系统
      if(this.data.openLightweightMember) {
        // 会员存在则执行支付操作，不存在则执行授权注册操作
        if(this.data.isExisted || this.data.canPay) {
          console.log('支付参数', params);
          this.wechatPay(params);
        } else {
          wx.hideToast();
          this.setData({
            submitLoading: false,
            showPhoneDialog: true
          })
        }
      } else {
          console.log('支付参数', params);
          this.wechatPay(params);
      }
      
    }).catch((err) => {
      this.setData({
        submitLoading: false
      });
      console.error(err);
    });
  },

  wechatPay: function (params) {
    wxPay(params).then((res) => {
      console.log(res);
      if (res.code === 200) {
        return res.data;
      } else {
        wx.hideToast();
        this.setData({
          submitLoading: false
        });

        if (res.msg === 'Token不能为空' || res.msg === 'Token失效' || res.msg === '会员不存在') {
          // 清除缓存
          wx.clearStorageSync();

          this.jumpToIndex();
        } else {
          wx.showModal({
            title: '支付失败提示',
            content: res.msg,
            showCancel: false,
            success(res) {
              if (res.confirm) {
                console.log('用户点击确定')
              } else if (res.cancel) {
                console.log('用户点击取消')
              }
            }
          });
          throw new Error(res.msg);
        }
      }
    }).then((data) => {
      if (data.paymentStatus === 102 && data.channelResult) {
        const channelResult = JSON.parse(data.channelResult);
        wx.requestPayment({
          timeStamp: channelResult.timeStamp,
          nonceStr: channelResult.nonceStr,
          package: channelResult.package,
          paySign: channelResult.paySign,
          signType: channelResult.signType,
          success: (res) => {
            wx.hideToast();
            this.setData({
              submitLoading: false
            });
            console.log(res, 'success');
            if (res.errMsg === 'requestPayment:ok') {
              console.log('支付成功');
              wx.showToast({
                title: '支付成功',
                icon: 'success',
                duration: 2000
              });
              this.setData({
                inputAmount: '',
                checked: false,
                usedPointsResult: null
              });
              this.initData();
            } else {
              wx.showToast({
                title: '支付完成！',
                icon: 'success',
                duration: 3000
              });
            }
          },
          fail: (res) => {
            wx.hideToast();
            this.setData({
              submitLoading: false
            });
            console.error(res);
            if (res.errMsg === 'requestPayment:fail cancel') {
              wx.showToast({
                title: '您已取消支付',
                icon: 'error',
                duration: 3000
              });
            } else {
              wx.showToast({
                title: res.errMsg,
                icon: 'error',
                duration: 3000
              });
            }
          }
        });
      } else {
        wx.hideToast();
        this.setData({
          submitLoading: false
        });
        wx.showModal({
          title: '支付失败提示',
          content: data.errMsg || '支付请求失败',
          showCancel: false,
          success(res) {
            if (res.confirm) {
              console.log('用户点击确定')
            } else if (res.cancel) {
              console.log('用户点击取消')
            }
          }
        });
      }
    });
  },

  changeCheck: function (e) {
    if (e.detail) {
      this.setData({
        oilLiters: this.data.usedPointsResult.oilLiters,
        directDiscount: this.data.usedPointsResult.directDiscount,
        orderFinalAmount: this.data.usedPointsResult.orderFinalAmount,
        platServiceFee: this.data.usedPointsResult.platServiceFee,
        checked: e.detail
      })
    } else {
      this.setData({
        oilLiters: this.data.originalResult.oilLiters,
        directDiscount: this.data.originalResult.directDiscount,
        orderFinalAmount: this.data.originalResult.orderFinalAmount,
        platServiceFee: this.data.originalResult.platServiceFee,
        checked: e.detail
      })
    }
  },

  clickDetail: function () {
    this.setData({
      showOverlay: !this.data.showOverlay
    })
  },

  clickHide: function () {
    this.setData({
      showOverlay: false
    })
  },

  jumpToIndex: function () {
    wx.showToast({
      title: '请重新扫码',
      icon: 'error',
      duration: 2000,
    }).then(res => {
      wx.redirectTo({
        url: '/pages/index/index',
      })
    });
  },

  closePhoneDialog: function () {
    this.setData({
      canPay: true,
      showPhoneDialog: false
    })
  },

  getPhoneNumber: function (e) {
    console.log(e)
    const {
      isExisted
    } = e.detail;

    this.setData({
      isExisted: isExisted,
      showPhoneDialog: false
    })

    if(isExisted) {
      this.computeFinal();
    }
  },

  initData: function () {
    this.setData({
      oilLiters: 0,
      otherAmount: 0,
      directDiscount: 0,
      orderFinalAmount: 0,
      platServiceFee: 0,
      actualMarketingAmount: 0
    })
  },

  /**
   * 生命周期函数--监听页面加载 
   */
  onLoad: function (options) {
    console.log(options.scene);

    if (options.scene) {
      // 清除缓存
      wx.clearStorageSync();

      app.globalData.userInfo.pcn = options.scene;
    } else {
      this.jumpToIndex();
    }
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    if (app.globalData.userInfo.pcn) {
      this.setData({
        pcn: app.globalData.userInfo.pcn
      })
    }

    this.qryPcn();
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {

  }
})