// pages/voucher/voucher.js

Page({

  /**
   * 页面的初始数据
   */
  data: {
    gdUrl: 'shareActivity/basic_activity/page/BasicActivityPop/BasicActivityPop?page_id=4qigi9WQMsG&gd_from=outside_coupon_&gdlm=1:2:aliyun-ym:QD-XMTP-423039:42a4d1:',
    gdAppId: 'wxbc0cf9b963bd3550'
  },

  jumpToTest: function () {
    wx.scanCode({
      success(res) {
        console.log(res)
        // 跳转路径
        wx.redirectTo({
          url: "/" + res.path,
        })
      },
      fail(error) {
        console.log('失败')
        console.log(error)
      }
    })
  },

  jumpToGd: function () {
    // 跳转小程序
    wx.navigateToMiniProgram({
      appId: this.data.gdAppId,
      path: this.data.gdUrl
    });
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function () {},

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {},

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {},

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {},

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {},

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {},

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {}
})