/* pages/voucher/voucher.wxss */
page {
  width: 100%;
  overflow: hidden;
}

.bg-image {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
}

.hover-btn {
  opacity: 0.9;
  transform: scale(0.95, 0.95);
  position: relative;
  top: 3rpx;
  left: 3rpx;
  box-shadow: 0px 0px 8px rgba(0, 0, 0, 0.1) inset;  
}

.logo {
  padding: 3vh 0;
}

.logo-image {
  width: 15vw;
  height: 15vw;
}

.gd-content {
  font-size: 35rpx;
  font-weight: 600;
  background-image: linear-gradient(to bottom, #FFF2D3, #FACE78);
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
}

.gd-title {
  font-size: 80rpx;
  font-weight: 800;
  padding: 3vh 0 3vh 0;
}

.envelope {
  margin-top: 3vh;
  position: relative
}

.envelope-content-header {
  position: absolute;
  top: 5vh;
  font-size: 32rpx;
  font-weight: 600;
  color: #FF444B;
}

.envelope-money {
  font-size: 140rpx;
  font-weight: 800;
}

.envelope-money::after {
  content: '元';
  font-size: 80rpx;
  background-image: linear-gradient(to bottom, #F86F51, #F60F0B);
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
}

.envelope-content-content { 
  position: absolute;
  bottom: 4vh;
}

.envelope-content-content-desc {
  padding: 2vh 0;
  font-size: 25rpx;
  color: #fff;
  text-align: center;
}

.gd-new-receive-btn {
  width: 60vw;
  font-size: 40rpx;
  font-weight: bold;
  border-radius: 10vw;
  color: #FE4C4D;
  background-image: linear-gradient(to bottom, #fca904, #f8d406);
}

.envelope-image {
  width: 80vw;
}

.gold-left-top {
  position: absolute;
  left: 5vw;
  top: 5vh;
  width: 12vw;
}

.gold-left-bottom {
  position: absolute;
  left: 9vw;
  bottom: 0;
  width: 8vw;
}

.gold-right-top {
  position: absolute;
  right: 1vw;
  top: 15vh;
  width: 10vw;
}

.gold-right-bottom {
  position: absolute;
  right: 3vw;
  bottom: 8vh;
  width: 10vw;
}