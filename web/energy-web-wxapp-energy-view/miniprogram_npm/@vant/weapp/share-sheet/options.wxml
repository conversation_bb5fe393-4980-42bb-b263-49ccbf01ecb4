<wxs src="../wxs/utils.wxs" module="utils" />
<wxs src="./options.wxs" module="computed" />

<view class="{{ utils.bem('share-sheet__options', { border: showBorder }) }}">
  <view
    wx:for="{{ options }}"
    wx:key="index"
    class="van-share-sheet__option"
    data-index="{{ index }}"
    bindtap="onSelect"
  >
    <button class="van-share-sheet__button" open-type="{{ item.openType }}">
      <image src="{{ computed.getIconURL(item.icon) }}" class="van-share-sheet__icon" /> 
      <view wx:if="{{ item.name }}" class="van-share-sheet__name">{{ item.name }}</view>
      <view wx:if="{{ item.description }}" class="van-share-sheet__option-description">
        {{ item.description }}
      </view>
    </button>
  </view>
</view>
