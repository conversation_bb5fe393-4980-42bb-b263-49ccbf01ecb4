import request from './request';

// 收款码小程序用户授权
export const wxAuth = (data) => {
  return request.wxRequest({
    url: '/miniApi/paymentCode/auth/wxAuth?code=' + data.code + '&pcn=' + data.pcn,
    method: 'POST'
  })
}

// 注册
export const newUserRegister = (data) => {
  return request.wxRequest({
    url: '/miniApi/paymentCode/member/register',
    method: 'POST',
    data,
  })
}

// 查询收款码信息
export const qryPcn = (data) => {
  return request.wxRequest({
    url: '/miniApi/paymentCode/pay/qryPcn',
    method: 'POST',
    data,
  })
}

// 支付
export const wxPay = (data) => {
  return request.wxRequest({
    url: '/miniApi/paymentCode/pay/wxPay',
    method: 'POST',
    data,
  })
}

// 计算油品订单金额
export const computeOrder = (data) => {
  return request.wxRequest({
    url: '/miniApi/paymentCode/pay/computeOrder',
    method: 'POST',
    data,
  })
}

// 查询收款结果
export const qryPayInfo = (data) => {
  return request.wxRequest({
    url: '/miniApi/paymentCode/pay/qryPayInfo',
    method: 'POST',
    data,
  })
}