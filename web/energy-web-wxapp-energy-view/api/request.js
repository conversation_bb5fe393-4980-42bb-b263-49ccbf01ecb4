const app = getApp();
const BASE_URL = app.globalData.BASE_URL;

class Request {
  constructor(options) {
    this._header = {
      ...options
    }
  }

  setError<PERSON>andler(handler) {
    this._errorHandler = handler;
  }

  wxRequest({ url, data, header, method }) {
    try {
      const value = wx.getStorageSync('token');
      if (value) {
        header = Object.assign({ 'accessToken': value });
      }
    } catch(e) {
      console.error(e);
    }

    return new Promise((resolve, reject) => {
      wx.request({
        url: BASE_URL + url,
        data,
        header,
        method,
        success: ((res) => {
          if (res.statusCode === 200) {
            resolve(res.data);
          } else {
            if (this._errorHandler !== null) {
              console.log("request: ", res);
            }
            reject(res.data);
          }
        }),
        fail: ((res) => {
          if (this._errorHandler !== null) {
            console.log("request: ", res);
          }
          reject(res);
        })
      });
    })
  }
}

const wxRequest = new Request();

export default wxRequest;