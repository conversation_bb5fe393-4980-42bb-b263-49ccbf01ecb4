const devBackEndURI = 'https://test.jayocun.com/dev/payapi'
const testBackEndURI = 'https://test.jayocun.com/test/payapi'
const prodBackEndURI = 'https://wx.jayocun.com'

// app.js
App({
  onLaunch() {
    this.autoUpdate();
  },

  // 版本更新
  autoUpdate: function() {
    let that = this;
    // 获取小程序更新机制兼容
    if(wx.canIUse('getUpdateManager')) {
      const updateManager = wx.getUpdateManager();
      //1. 检查小程序是否有新版本发布
      updateManager.onCheckForUpdate(function (res) {
        console.log(res)
        // 请求完新版本信息的回调
        if(res.hasUpdate) {
           //2. 小程序有新版本，则静默下载新版本，做好更新准备
           updateManager.onUpdateReady(function () {
            wx.showModal({
              title: '更新提示',
              content: '新版本已经准备好，是否重启应用？',
              showCancel: false, // 变相强制更新
              success(res) {
                console.log(res);
                if (res.confirm) {
                  //3. 新的版本已经下载好，调用 applyUpdate 应用新版本并重启
                  updateManager.applyUpdate()
                } else if (res.cancel) { 
                  // 不需要强制更新的话这里不需要
                  // wx.showModal({
                  //   title: '温馨提示~',
                  //   content: '本次版本更新涉及到新的功能添加，旧版本无法正常访问哦~',
                  //   success: function (res) {                   
                  //     // 第二次提示后，强制更新                      
                  //     if (res.confirm) {
                  //       // 新的版本已经下载好，调用 applyUpdate 应用新版本并重启
                  //       updateManager.applyUpdate()
                  //     } else if (res.cancel) {
                  //       // 重新回到版本更新提示
                  //       that.autoUpdate()
                  //     }
                  //   }
                  // })
                }
              }
            })
          })

           updateManager.onUpdateFailed(function () {
            // 新的版本下载失败
            wx.showModal({
              title: '已经有新版本了哟~',
              content: '新版本已经上线啦~，请您删除当前小程序，重新搜索打开哟~',
            })
          }) 
        }
      })
    } else {
      wx.showModal({
        title: '提示',
        content: '当前微信版本过低，影响功能正常使用，请升级到最新微信版本后重试。'
      })
    }
  },
  
  globalData: {
    // BASE_URL: devBackEndURI,
    BASE_URL: testBackEndURI,
    // BASE_URL: prodBackEndURI,
    userInfo: {
      pcn: '',
      openId: '',
      unionId: '',
      memberId: '',
      paymentCodeInfoDTO: null
    },
    TOKEN: null,
  }
})
